package mapper

import (
	"context"
	"correspondence-composer/models"
	xmlgenmodels "correspondence-composer/models/generated"
	"correspondence-composer/utils"
	"fmt"
	"strconv"
	"strings"
)

func MapLCPolicySurrenderChargesToPolicy(data *models.Policy, productRates []*models.AnnuityProductRates, productRateMappings []models.ProductRateXMLSurrenderChrages) *xmlgenmodels.SURRENDERCHARGES {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var xmlPolicySurrenderSchedules []*xmlgenmodels.SURRENDERCHARGE
	// Check for policy funds
	if len(data.DistributionDetails.PolicyFunds) == 0 {
		return nil
	}
	interestGuaranteedPeriod := data.DistributionDetails.PolicyFunds[0].InterestGuaranteedPeriod

	for _, rate := range productRates {
		for _, mapping := range productRateMappings {
			if rate.RateId == mapping.RateID {
				// Pass the appropriate ContractYear map based on SurrenderChargeType
				if len(mapping.Occurance) == 0 {
					if mapping.APIEndpoint == "ContractYear" {
						processInitialSurrenderCharges(rate, interestGuaranteedPeriod, &xmlPolicySurrenderSchedules, mapping.SurrenderChargeType, mapping.FilterByYear)
					} else if mapping.APIEndpoint == "GuaranteedYear" {
						processSubsequentSurrenderCharges(rate, interestGuaranteedPeriod, &xmlPolicySurrenderSchedules, mapping.SurrenderChargeType)
					}
				}
			}
		}
	}

	return &xmlgenmodels.SURRENDERCHARGES{
		SURRENDERCHARGE: xmlPolicySurrenderSchedules,
	}
}

// Function to process initial surrender charges
func processInitialSurrenderCharges(rate *models.AnnuityProductRates, interestGuaranteedPeriod int, xmlPolicySurrenderSchedules *[]*xmlgenmodels.SURRENDERCHARGE, cType string, FilterByYear bool) {
	if rate != nil && rate.ContractYear.ContractYear != nil {
		for cYear, details := range rate.ContractYear.ContractYear {
			//The filtering logic based on Interest Guarantee Period (PVAL_MIN_INIT_PCT) must be skipped for FIA and needed for MYGA, Which is handling thorgh Rules.
			if cYearInt, err := strconv.Atoi(strings.Split(cYear, ".")[0]); err == nil && ((FilterByYear && cYearInt <= interestGuaranteedPeriod) || !FilterByYear) {
				//if cYearInt, _ := strconv.Atoi(cYear); cYearInt <= interestGuaranteedPeriod {
				for _, detail := range details {
					if detail.EffectiveDate != nil {
						for _, dateVal := range detail.EffectiveDate {
							surrenderschedule := &xmlgenmodels.SURRENDERCHARGE{
								SURRCHRGTERMPERC: dateVal,
								SURRCHRGTERMYEAR: cYearInt,
								SURRCHRGTYPE:     cType,
							}
							*xmlPolicySurrenderSchedules = append(*xmlPolicySurrenderSchedules, surrenderschedule)
						}
					}
				}
			} else {
				fmt.Printf("Skipping Year: %s, Error: %v\n", cYear, err) // Debug print
			}
		}
	}
}

func processSubsequentSurrenderCharges(rate *models.AnnuityProductRates, interestGuaranteedPeriod int, xmlPolicySurrenderSchedules *[]*xmlgenmodels.SURRENDERCHARGE, cType string) {
	for gYear, details := range rate.GuaranteedYear.GuaranteedYear {
		if gYearInt, err := strconv.Atoi(strings.Split(gYear, ".")[0]); err == nil && gYearInt <= interestGuaranteedPeriod {
			//if cYearInt, _ := strconv.Atoi(cYear); cYearInt <= interestGuaranteedPeriod {
			for _, detail := range details {
				if detail.EffectiveDate != nil {
					for _, dateVal := range detail.EffectiveDate {
						surrenderschedule := &xmlgenmodels.SURRENDERCHARGE{
							SURRCHRGTERMPERC: dateVal,
							SURRCHRGTERMYEAR: gYearInt,
							SURRCHRGTYPE:     cType,
						}
						*xmlPolicySurrenderSchedules = append(*xmlPolicySurrenderSchedules, surrenderschedule)
					}
				}
			}
		} else {
			fmt.Printf("Skipping Year: %s, Error: %v\n", gYear, err) // Debug print
		}
	}
}

func MapZaharaAnnuitySurrenderChargesToPolicy(data *models.Policy, productRates []*models.ProductRates, productRateMappings []models.ProductRateXMLSurrenderChrages) *xmlgenmodels.SURRENDERCHARGES {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var xmlSurrenderCharges []*xmlgenmodels.SURRENDERCHARGE
	var guaranteedPeriod []interface{}
	var initialGuaranteedPeriod int

	for _, rate := range productRates {
		for _, mapping := range productRateMappings {
			if rate != nil && rate.RateId == mapping.RateID {
				if mapping.APIEndpoint == "GuaranteedPeriod" {
					if rate.Rates != nil && rate.Rates.Ages != nil && rate.Rates.Ages.Age != nil {
						guaranteedPeriod = rate.Rates.Ages.Age
						if len(guaranteedPeriod) > 0 {
							initialGuaranteedPeriod = int(guaranteedPeriod[0].(float64)) // Extract the number of years
						}
					}
				}
				if mapping.APIEndpoint == "SurrChargePercentage" {
					if rate.Rates != nil && rate.Rates.Ages != nil && rate.Rates.Ages.Age != nil {
						for i := 0; i < initialGuaranteedPeriod; i++ {
							termYear := i
							if i < len(rate.Rates.Ages.Age) { // Check to prevent index out-of-bounds
								surrenderCharge := &xmlgenmodels.SURRENDERCHARGE{
									SURRCHRGTERMPERC: strconv.FormatFloat(rate.Rates.Ages.Age[i].(float64), 'f', 2, 64),
									SURRCHRGTERMYEAR: termYear + 1,
									SURRCHRGTYPE:     mapping.SurrenderChargeType,
								}
								xmlSurrenderCharges = append(xmlSurrenderCharges, surrenderCharge)
							}
						}
					}
				}
			}
		}
	}
	return &xmlgenmodels.SURRENDERCHARGES{
		SURRENDERCHARGE: xmlSurrenderCharges,
	}
}

func MapSurrenderChargesToPolicy(data *models.Policy, productRates []*models.ProductRates, productRateMappings []models.ProductRateXMLSurrenderChrages) *xmlgenmodels.SURRENDERCHARGES {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var xmlSurrenderCharges []*xmlgenmodels.SURRENDERCHARGE

	for _, rate := range productRates {
		for _, mapping := range productRateMappings {
			if rate != nil && rate.RateId == mapping.RateID {
				if mapping.APIEndpoint == "SurrChargePercentage" {
					if rate.Rates != nil && rate.Rates.Ages != nil && rate.Rates.Ages.Age != nil {
						for i := 0; i < len(rate.Rates.Ages.Age); i++ {
							surrenderCharge := &xmlgenmodels.SURRENDERCHARGE{
								SURRCHRGTERMPERC: strconv.FormatFloat(rate.Rates.Ages.Age[i].(float64), 'f', 2, 64),
								SURRCHRGTERMYEAR: i + 1,
								SURRCHRGTYPE:     mapping.SurrenderChargeType,
							}
							xmlSurrenderCharges = append(xmlSurrenderCharges, surrenderCharge)
						}
					}
				}
			}
		}
	}

	return &xmlgenmodels.SURRENDERCHARGES{
		SURRENDERCHARGE: xmlSurrenderCharges,
	}
}