package mapper

import (
	helpers "correspondence-composer/mapper/helpers"
	"correspondence-composer/models"
	xmlgenmodels "correspondence-composer/models/generated"
	"correspondence-composer/utils"
	"correspondence-composer/utils/log"
	"fmt"
	"strconv"
	"strings"

	//"golang.org/x/tools/godoc/util"
	"context"
	conf "correspondence-composer/config"
)

func MapSuitabilityDataToXML(allPolicyData *models.AllPolicyData, correlationID string, ruleResults *models.RulesResults, event *models.EventEnvelope, targetrole string) (*xmlgenmodels.POLICY, error) {
	ctx = context.WithValue(ctx, "CorrelationID", correlationID)
	defer utils.PanicHandler(ctx)
	var prefMgmtEmailOwnerIdentifier, prefMgmtDeliveryOptionOwnerIdentifier, applicationID, productType, polNum, productMktgName, qualificationType string
	var caseStore *models.CaseStore
	var applicationStore *models.CaseStore
	//caseStoreData := allPolicyData.CaseStore
	carrierData := allPolicyData.Carrier
	agentdata := allPolicyData.PomAgent
	//To set delivery tags for owner

	for _, identifier := range event.Identifiers {
		if identifier != nil && identifier.IdentifierType == "prefMgmtEmail" {
			prefMgmtEmailOwnerIdentifier = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "prefMgmtDeliveryOption" {
			prefMgmtDeliveryOptionOwnerIdentifier = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "applicationId" {
			applicationID = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "policyNumber" {
			polNum = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "policyAPILatestVersionResponse" {
			productType = allPolicyData.PolicyAPIResponse.Data.Product.ProductType
			qualificationType = allPolicyData.PolicyAPIResponse.Data.QualificationType
		}
	}

	for _, casestr := range event.CaseStore {
		if casestr.EntityType == "NB_SUITABILITY_DATA" {
			productMktgName = casestr.Entity.SuitabilityRequest.PolicyData.ProductType
			caseStore = casestr
		}
		if casestr.EntityType == "NB_APPLICATION_DATA" {
			applicationStore = casestr
		}
	}
	policy := &xmlgenmodels.POLICY{
		POLPRDCTMKTGNAME: productMktgName,
		POLLOB:           ruleResults.LineOfBusiness,
		POLCASEID:        allPolicyData.Policy.PolicyNumber,
		POLAPPLICATIONID: applicationID,
		POLTRACKINGID:    correlationID,
		POLPLANCODE:      allPolicyData.Policy.Product.PlanCode,
		PARTIES:          mapPartyDataToPolicy(applicationStore, caseStore, ruleResults, targetrole, agentdata, ruleResults.AgentMappingVariables, prefMgmtDeliveryOptionOwnerIdentifier, prefMgmtEmailOwnerIdentifier),
		CARRIER:          helpers.MapCarrierDataToPolicy(carrierData, nil, ruleResults),
		POLPRODUCTTYPE:   productType,
		POLPOLNUM:        polNum,
		POLCONT:          polNum,
		POLQUALDESC:      qualificationType,
	}

	financialInfo, err := MapFiscalDataToPolicy(caseStore, ruleResults, targetrole, agentdata, ruleResults.AgentMappingVariables, correlationID)
	if err != nil {
		return nil, err
	}
	policy.FINANCIALINFORMATION = financialInfo

	//Cant set below fields in above helper function as its generic function. For other flow it is getting set form policy.carrierID
	policy.CARRIER.CARRIERCODE = carrierData.CarrierID
	policy.POLPRCSGCOMP = carrierData.CarrierBusinessName

	return policy, nil
}

func MapFiscalDataToPolicy(eData *models.CaseStore, ruleResults *models.RulesResults, targetrole string, pomAgent *models.POMApiResponse, agentMappingVariables *models.AgentMappingVariable, correlationID string) (*xmlgenmodels.FINANCIALINFORMATION, error) {
	logger := log.New(log.Config{
		ServiceName: "correspondence-composer",
	})
	config := conf.GetConfig(logger)
	var annualIncomeFloat, annualExpensesFloat float64
	var annualIncome, annualExpenses, financialChangeAcknowledgement, federalTaxBand, netWorth, addressType, liquidAsset, totalAnnuityValue string
	var distributionMethods, sourceOfFunds, optedYears, mappedReasons, productNames []string

	if eData != nil && eData.Entity != nil && eData.Entity.SuitabilityRequest != nil && eData.Entity.SuitabilityRequest.FiscalData != nil {
		annualIncome = eData.Entity.SuitabilityRequest.FiscalData.AnnualIncome
		annualExpenses = eData.Entity.SuitabilityRequest.FiscalData.AnnualExpenses

		if annualIncome != "" {
			annualIncomeFloat, _ = strconv.ParseFloat(eData.Entity.SuitabilityRequest.FiscalData.AnnualIncome, 64)
		}
		if annualExpenses != "" {
			annualExpensesFloat, _ = strconv.ParseFloat(eData.Entity.SuitabilityRequest.FiscalData.AnnualExpenses, 64)
		}
		financialChangeAcknowledgement = eData.Entity.SuitabilityRequest.FiscalData.SurrPeriodMaterialChangeExp

		// TODO Migrate to BPM rules
		// Map sourceOfFunds to display values
		for _, source := range eData.Entity.SuitabilityRequest.FiscalData.SourceOfFunds {
			switch source {
			case "FIXED_ANNUITY":
				sourceOfFunds = append(sourceOfFunds, "Fixed annuity")
			case "INDEXED_ANNUITY":
				sourceOfFunds = append(sourceOfFunds, "Indexed annuity")
			case "LIFE_INSURENCE":
				sourceOfFunds = append(sourceOfFunds, "Life insurance")
			case "CHECKING_SAVING_ACCOUNT":
				sourceOfFunds = append(sourceOfFunds, "Checking/savings account")
			case "CD":
				sourceOfFunds = append(sourceOfFunds, "CD")
			case "VARIABLE_ANNUITY":
				sourceOfFunds = append(sourceOfFunds, "Variable annuity")
			case "REVERSE_MORTGAGE":
				sourceOfFunds = append(sourceOfFunds, "Reverse Mortgage")
			case "DEATH_BENEFIT":
				sourceOfFunds = append(sourceOfFunds, "Death benefit")
			case "INHERITED_IRA":
				sourceOfFunds = append(sourceOfFunds, "Inherited IRA")
			case "INHERITANCE":
				sourceOfFunds = append(sourceOfFunds, "Inheritance")
			case "401K_ROLLOVER":
				sourceOfFunds = append(sourceOfFunds, "401(k) rollover")
			case "RETIREMENT_PLAN":
				sourceOfFunds = append(sourceOfFunds, "Retirement plan")
			case "STOCKS_BONDS_MF":
				sourceOfFunds = append(sourceOfFunds, "Stocks/bonds/mutual funds")
			case "OTHERS":
				sourceOfFunds = append(sourceOfFunds, "Other")
			case "SPOUSAL_CONTRIBUTION":
				sourceOfFunds = append(sourceOfFunds, "Spousal contribution")
			default:
				logger.Log(log.Info_Log, fmt.Sprintf("No required value found for SuitabilityRequest.FiscalData.SourceOfFunds in Case Store API. Proceeding further. Actual value found is %s", source), log.Fields{
					"CorrelationID": correlationID,
					"MethodName":    "MapFiscalDataToPolicy",
					"env":           config.Environment,
				})
			}
		}
		federalTaxBand = eData.Entity.SuitabilityRequest.FiscalData.FederalTaxBand
		netWorth = eData.Entity.SuitabilityRequest.FiscalData.NetWorth
	}

	// TODO Migrate to BPM rules
	// Map DistributionMethods to display values
	if eData != nil && eData.Entity != nil && eData.Entity.SuitabilityRequest != nil && eData.Entity.SuitabilityRequest.LiquidityOptions != nil {
		if eData.Entity.SuitabilityRequest.LiquidityOptions.FutureDistributionMethods != nil {
			for _, item := range eData.Entity.SuitabilityRequest.LiquidityOptions.FutureDistributionMethods {

				switch item.DistributionMethods {
				case "FREE_WITHDRAWAL":
					distributionMethods = append(distributionMethods, "Penalty-free withdrawal")
				case "ANNUITIZATION":
					distributionMethods = append(distributionMethods, "Annuitization")
				case "LUMP_SUM":
					distributionMethods = append(distributionMethods, "Lump sum")
				case "RMD":
					distributionMethods = append(distributionMethods, "Required Minimum Distribution")
				case "NO_PLANS":
					distributionMethods = append(distributionMethods, "No current plans to access")
				default:
					logger.Log(log.Info_Log, fmt.Sprintf("No required value found for SuitabilityRequest.LiquidityOptions.FutureDistributionMethods in Case Store API. Proceeding further. Actual value found is %s", item.DistributionMethods), log.Fields{
						"CorrelationID": correlationID,
						"MethodName":    "MapFiscalDataToPolicy",
						"env":           config.Environment,
					})
				}

				if item.OptedYears != "" {
					optedYears = append(optedYears, item.OptedYears)
				}
			}
		}

	}

	if eData != nil && eData.Entity != nil && eData.Entity.SuitabilityRequest != nil &&
		eData.Entity.SuitabilityRequest.FinancialExperience != nil {
		if eData.Entity.SuitabilityRequest.FinancialExperience.FinancialProducts != nil && eData.Entity.SuitabilityRequest.FinancialExperience.FinancialProducts.TotalAsset != nil {
			for _, asset := range eData.Entity.SuitabilityRequest.FinancialExperience.FinancialProducts.TotalAsset {
				if asset.AssetType == "LIQUID_ASSET" {
					liquidAsset = asset.Amount
					break
				}
			}
		}
		totalAnnuityValue = eData.Entity.SuitabilityRequest.FinancialExperience.TotalAnnuityValue
	}

	if eData.EntityType == "NB_SUITABILITY_DATA" {
		for _, party := range eData.Entity.SuitabilityRequest.Parties {
			if party.RoleType == "OWNER" && len(party.Addresses) > 0 {
				addressType = party.Addresses[0].AddressType
			}
		}
	}

	// TODO Migrate to BPM rules
	// Map FINANCIALPRODUCTS to display values
	if eData != nil && eData.Entity != nil && eData.Entity.SuitabilityRequest != nil && eData.Entity.SuitabilityRequest.FinancialExperience != nil && eData.Entity.SuitabilityRequest.FinancialExperience.FinancialProductOwnerships != nil {
		for _, item := range eData.Entity.SuitabilityRequest.FinancialExperience.FinancialProductOwnerships {
			switch item.ProductName {
			case "FIXED_ANNUITIES":
				productNames = append(productNames, "Fixed/variable annuity")
			case "CERTIFICATE_OF_DEPOSITE":
				productNames = append(productNames, "Certificates of deposit")
			case "STOCKS_BONDS_MF":
				productNames = append(productNames, "Stocks/bonds/mutual funds")
			case "OTHERS":
				productNames = append(productNames, "Other")
			default:
				logger.Log(log.Info_Log, fmt.Sprintf("No required value found for SuitabilityRequest.FinancialExperience.FinancialProductOwnerships.ProductType in Case Store API. Proceeding further. Actual value found is %s", item.ProductName), log.Fields{
					"env":           config.Environment,
					"CorrelationID": correlationID,
					"MethodName":    "MapFiscalDataToPolicy",
				})
			}
		}

	}
	// TODO Migrate to BPM rules
	// Map PurchaseReason to display values
	if eData != nil && eData.Entity != nil && eData.Entity.SuitabilityRequest != nil && eData.Entity.SuitabilityRequest.FinancialObjectives != nil && eData.Entity.SuitabilityRequest.FinancialObjectives.PurchaseReason != nil {
		for _, item := range eData.Entity.SuitabilityRequest.FinancialObjectives.PurchaseReason {
			switch item {
			case "INCOME":
				mappedReasons = append(mappedReasons, "Income now")
			case "GURANTEES":
				mappedReasons = append(mappedReasons, "Provides guarantees")
			case "PRINCIPAL_PROTECTION":
				mappedReasons = append(mappedReasons, "Principal protection")
			case "OTHERS":
				mappedReasons = append(mappedReasons, "Other")
			case "GROWTH":
				mappedReasons = append(mappedReasons, "Accumulation/Growth")
			case "TAX_DEFERRAL":
				mappedReasons = append(mappedReasons, "Tax deferral")
			default:
				logger.Log(log.Info_Log, fmt.Sprintf("No required value found for SuitabilityRequest.FinancialObjectives.PurchaseReason in Case Store API. Proceeding further. Actual value found is %s", item), log.Fields{
					"CorrelationID": correlationID,
					"MethodName":    "MapFiscalDataToPolicy",
					"env":           config.Environment,
				})
			}
		}
	}

	// Create a helper function to generate xmlFinancialInfo from a party type
	xmlFinancialInfo := &xmlgenmodels.FINANCIALINFORMATION{
		FINANCIALANNUALINCOME:          annualIncome,
		FINANCIALANNUALEXPENSES:        annualExpenses,
		FINANCIALDISPOSABLEINCOME:      fmt.Sprintf("%f", annualIncomeFloat-annualExpensesFloat),
		FINANCIALCHANGEACKNOWLEDGEMENT: financialChangeAcknowledgement,
		FINANCIALRESIDENCETYPE:         addressType,
		FINANCIALSUFFICIENTFUND: func() string {
			if eData != nil && eData.Entity != nil && eData.Entity.SuitabilityRequest != nil && eData.Entity.SuitabilityRequest.AdditionalInfo != nil && eData.Entity.SuitabilityRequest.AdditionalInfo.ExtraFundAvailable != "" {
				return eData.Entity.SuitabilityRequest.AdditionalInfo.ExtraFundAvailable
			}
			return ""
		}(),
		FINANCIALTAXBRACKET:  federalTaxBand,
		FINANCIALNETWORTH:    netWorth,
		FINANCIALLIQUIDASSET: liquidAsset,
		FINANCIALCABENEFIT: func() string {
			if eData != nil && eData.Entity != nil && eData.Entity.SuitabilityRequest != nil && eData.Entity.SuitabilityRequest.CaliforniaResidentQuestions != nil {
				if eData.Entity.SuitabilityRequest.CaliforniaResidentQuestions.ApplyForMeansTestedBenefits == "" {
					return "N/A"
				}
				return eData.Entity.SuitabilityRequest.CaliforniaResidentQuestions.ApplyForMeansTestedBenefits
			}
			return "N/A"
		}(),
		FINANCIALPRODUCTS:           strings.Join(productNames, "/ "),
		FINANCIALTOTALANNUITYVALUE:  totalAnnuityValue,
		FINANCIALPOTENTIALINTERESTS: strings.Join(mappedReasons, "/ "),
		FINANCIALSOURCEOFFUNDS:      strings.Join(sourceOfFunds, "/ "),
		FINANCIALREPLACEMENTS: func() string {
			if eData != nil && eData.Entity != nil && eData.Entity.SuitabilityRequest != nil && eData.Entity.SuitabilityRequest.ApplicationInfo != nil && eData.Entity.SuitabilityRequest.ApplicationInfo.ReplacementInd != "" {
				return eData.Entity.SuitabilityRequest.ApplicationInfo.ReplacementInd
			}
			return ""
		}(),
		FINANCIALDISTRIBUTIONS:     strings.Join(distributionMethods, "/ "),
		FINANCIALFIRSTDISTRIBUTION: strings.Join(optedYears, "/ "),
	}
	return xmlFinancialInfo, nil
}

func mapPartyDataToPolicy(aData *models.CaseStore, eData *models.CaseStore, ruleResults *models.RulesResults, targetrole string, pomAgent *models.POMApiResponse,
	agentMappingVariables *models.AgentMappingVariable, prefMgmtDeliveryOptionOwnerIdentifier string, prefMgmtEmailIdentifier string) *xmlgenmodels.PARTIES {
	var xmlParties []*xmlgenmodels.PARTY

	// Create a helper function to generate xmlParty from a party type
	createXmlParty := func(RoleType string, party *models.CaseStoreParty, pomAgent *models.POMApiResponse, agentMappingVariables *models.AgentMappingVariable) *xmlgenmodels.PARTY {
		xmlParty := &xmlgenmodels.PARTY{
			PARTYFULLNAME: party.FullName,
			PARTYFSTNAME:  party.Person.FirstName,
			PARTYMI:       party.Person.MiddleName,
			PARTYLSTNAME:  party.Person.LastName,
			DELIVERY:      mapDeliveryToParty(party, ruleResults, RoleType, prefMgmtDeliveryOptionOwnerIdentifier, targetrole),
			ROLES:         mapRolesToParty(RoleType, ruleResults),
			ADDRESSES: &xmlgenmodels.ADDRESSES{
				ADDRESS: mapAddressToParty(party),
			},
			EMAILADDRESSES: &xmlgenmodels.EMAILADDRESSES{
				EMAILADDRESS: mapEmailsToParty(RoleType, party, ruleResults, targetrole, prefMgmtEmailIdentifier),
			},
		}
		if RoleType == agentMappingVariables.AgentPartyRoleType {
			utils.SetAgentXMLTags(ruleResults.AgentMappingVariables, pomAgent, xmlParty)
		}

		return xmlParty
	}

	// Create a helper function to generate xmlParty from a role type
	createXmlRole := func(RoleType string, role *models.Role, pomAgent *models.POMApiResponse, agentMappingVariables *models.AgentMappingVariable) *xmlgenmodels.PARTY {
		xmlParty := &xmlgenmodels.PARTY{
			PARTYFULLNAME: role.FirstName + " " + role.LastName,
			PARTYFSTNAME:  role.FirstName,
			PARTYMI:       role.MiddleName,
			PARTYLSTNAME:  role.LastName,
			DELIVERY:      mapDeliveryToPartyRole(role, ruleResults, RoleType, prefMgmtDeliveryOptionOwnerIdentifier, targetrole),
			ROLES:         mapRolesToParty(RoleType, ruleResults),
			ADDRESSES: &xmlgenmodels.ADDRESSES{
				ADDRESS: mapAddressToPartyRole(role),
			},
			EMAILADDRESSES: &xmlgenmodels.EMAILADDRESSES{
				EMAILADDRESS: mapEmailsToPartyRole(RoleType, role, ruleResults, targetrole, prefMgmtEmailIdentifier),
			},
		}
		return xmlParty
	}

	for _, party := range eData.Entity.SuitabilityRequest.Parties {
		if party.RoleType == "OWNER" {
			xmlParty := createXmlParty("OWNER", &party, nil, agentMappingVariables)
			xmlParties = append(xmlParties, xmlParty)
		}
		if party.RoleType == "PRIMARY_SERVICING_AGENT" {
			xmlParty := createXmlParty("PRIMARYSERVICINGAGENT", &party, pomAgent, agentMappingVariables)
			xmlParties = append(xmlParties, xmlParty)
		}
	}
	for _, role := range aData.Entity.Application.Roles {
		if role.RoleName == "PRIMARY_ANNUITANT" {
			xmlParty := createXmlRole("ANNUITANT", role, nil, agentMappingVariables)
			xmlParties = append(xmlParties, xmlParty)
		}
	}
	return &xmlgenmodels.PARTIES{PARTY: xmlParties}
}

func mapEmailsToParty(roleType string, party *models.CaseStoreParty, ruleResults *models.RulesResults, targetrole string, prefMgmtEmailIdentifier string) []*xmlgenmodels.EMAILADDRESS {
	var emailAddress string
	var partyEmails []*xmlgenmodels.EMAILADDRESS
	if roleType == targetrole && prefMgmtEmailIdentifier != "" {
		emailAddress = prefMgmtEmailIdentifier

		partyEmail := &xmlgenmodels.EMAILADDRESS{
			EMAILADDR:         emailAddress,
			EMAILPREFERREDIND: strconv.Itoa(1),
		}
		partyEmails = append(partyEmails, partyEmail)
		return partyEmails
	} else {
		return nil

	}
}
func mapEmailsToPartyRole(roleType string, role *models.Role, ruleResults *models.RulesResults, targetrole string, prefMgmtEmailIdentifier string) []*xmlgenmodels.EMAILADDRESS {
	var emailAddress string
	var partyEmails []*xmlgenmodels.EMAILADDRESS
	if roleType == targetrole && prefMgmtEmailIdentifier != "" {
		emailAddress = prefMgmtEmailIdentifier
	} else {
		emailAddress = role.Email
	}

	partyEmail := &xmlgenmodels.EMAILADDRESS{
		EMAILADDR:         emailAddress,
		EMAILPREFERREDIND: strconv.Itoa(1),
	}
	partyEmails = append(partyEmails, partyEmail)
	return partyEmails
}

func mapRolesToParty(party string, ruleResults *models.RulesResults) *xmlgenmodels.ROLES {
	var roleData []*xmlgenmodels.ROLE
	partyRefTableData := helpers.GetPartyRuleData(party, ruleResults.PartyRefs)
	var r *xmlgenmodels.ROLE

	if partyRefTableData != nil {
		r = &xmlgenmodels.ROLE{
			ROLEID:   strconv.Itoa(partyRefTableData.RoleID),
			ROLECODE: partyRefTableData.RoleCode,
			ROLETYPE: partyRefTableData.RoleType,
		}
		if len(partyRefTableData.RoleCC) > 0 {
			for _, ccRole := range partyRefTableData.RoleCC {
				if ccRole == partyRefTableData.RoleID {
					r.ROLECCIND = "YES"
				}
			}
			if !(r.ROLECCIND == "YES") {
				r.ROLECCIND = "NO"
			}
		}
	}

	roleData = append(roleData, r)

	ROLES := &xmlgenmodels.ROLES{
		ROLE: roleData,
	}
	return ROLES
}

func mapDeliveryToParty(party *models.CaseStoreParty, ruleResults *models.RulesResults, RoleType string, prefMgmtDeliveryOptionOwnerIdentifier string, targetRole string) *xmlgenmodels.DELIVERY {
	//TODO Refactor code: Use rule RoleEmailAndDeliveryIndForNB and remove prefMgmtDeliveryOptionOwnerIdentifier & prefMgmtEmailOwnerIdentifier from code for suitability
	if prefMgmtDeliveryOptionOwnerIdentifier != "" {
		party.DeliveryType = prefMgmtDeliveryOptionOwnerIdentifier
	}
	if RoleType == targetRole {
		return &xmlgenmodels.DELIVERY{
			RELATEDROLEID:                  ruleResults.DeliveryRef.RelatedRoleID,
			PARTYDELIVERYIND:               ruleResults.DeliveryRef.PartyDeliveryInd,
			PARTYDELIVERYTYPE:              party.DeliveryType,
			PARTYDELIVERYEMAILNOTIFICATION: party.EmailNotification,
		}
	} else if len(ruleResults.AgentDeliveryRef.RelatedRoleID) > 0 && RoleType == "PRIMARYSERVICINGAGENT" {
		return &xmlgenmodels.DELIVERY{
			RELATEDROLEID:                  ruleResults.AgentDeliveryRef.RelatedRoleID,
			PARTYDELIVERYIND:               ruleResults.AgentDeliveryRef.PartyDeliveryInd,
			PARTYDELIVERYTYPE:              ruleResults.AgentDeliveryRef.PartyDeliveryType,
			PARTYDELIVERYEMAILNOTIFICATION: ruleResults.AgentDeliveryRef.PartyDeliveryEmailNotification,
		}

	} else {
		return nil
	}
}
func mapDeliveryToPartyRole(role *models.Role, ruleResults *models.RulesResults, RoleType string, prefMgmtDeliveryOptionOwnerIdentifier string, targetRole string) *xmlgenmodels.DELIVERY {
	//TODO Refactor code: Use rule RoleEmailAndDeliveryIndForNB and remove prefMgmtDeliveryOptionOwnerIdentifier & prefMgmtEmailOwnerIdentifier from code for suitability
	if prefMgmtDeliveryOptionOwnerIdentifier != "" {
		role.DeliveryType = prefMgmtDeliveryOptionOwnerIdentifier
	}
	if RoleType == targetRole {
		return &xmlgenmodels.DELIVERY{
			RELATEDROLEID:                  ruleResults.DeliveryRef.RelatedRoleID,
			PARTYDELIVERYIND:               ruleResults.DeliveryRef.PartyDeliveryInd,
			PARTYDELIVERYTYPE:              role.DeliveryType,
			PARTYDELIVERYEMAILNOTIFICATION: role.EmailNotification,
		}
	} else {
		return nil
	}
}
func mapAddressToParty(party *models.CaseStoreParty) []*xmlgenmodels.ADDRESS {

	var address []*xmlgenmodels.ADDRESS
	for _, addr := range party.Addresses {
		if addr.AddressType == "RESIDENCE" {
			address = []*xmlgenmodels.ADDRESS{{
				ADDRLINE1:   addr.Line1,
				ADDRLINE2:   addr.Line2,
				ADDRLINE3:   addr.Line3,
				ADDRCITY:    addr.City,
				ADDRSTATE:   addr.State,
				ADDRZIP:     addr.Zip,
				ADDRPREFIND: "1", //For NB app, it will always have only one active address. and to implement in rules we dont have any inputs to give
			}}
			break
		} else {
			address = nil
		}
	}
	return address
}

func mapAddressToPartyRole(role *models.Role) []*xmlgenmodels.ADDRESS {

	var address []*xmlgenmodels.ADDRESS
	for _, addr := range role.Addresses {

		var line1, line2, line3 string

		if addr != nil && len(addr.AddressLines) > 0 {
			switch len(addr.AddressLines) {
			case 1:
				line1 = addr.AddressLines[0]
			case 2:
				line1 = addr.AddressLines[0]
				line2 = addr.AddressLines[1]
			default: // 3 or more
				line1 = addr.AddressLines[0]
				line2 = addr.AddressLines[1]
				line3 = addr.AddressLines[2]
			}
		}

		address = []*xmlgenmodels.ADDRESS{{
			ADDRLINE1:   line1,
			ADDRLINE2:   line2,
			ADDRLINE3:   line3,
			ADDRCITY:    addr.City,
			ADDRSTATE:   addr.State,
			ADDRZIP:     addr.Zip,
			ADDRPREFIND: "1", //For NB app, it will always have only one active address. and to implement in rules we dont have any inputs to give
		}}
	}
	return address
}
