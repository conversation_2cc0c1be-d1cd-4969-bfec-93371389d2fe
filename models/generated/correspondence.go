// Code generated by xgen. DO NOT EDIT.

package schema

import (
	"encoding/xml"
)

// AlphanumericString ...
type AlphanumericString string

// PhoneNumber ...
type PhoneNumber string

// Email ...
type Email string

// OptionalDate ...
type OptionalDate string

// POLAMENDMENT ...
type POLAMENDMENT struct {
	XMLName          xml.Name `xml:"POL_AMENDMENT"`
	POLAMENDMENTCODE string   `xml:"POL_AMENDMENT_CODE,omitempty"`
	POLAMENDMENTDESC string   `xml:"POL_AMENDMENT_DESC,omitempty"`
}

// POLAMENDMENTS ...
type POLAMENDMENTS struct {
	XMLName      xml.Name        `xml:"POL_AMENDMENTS"`
	POLAMENDMENT []*POLAMENDMENT `xml:"POL_AMENDMENT,omitempty"`
}

// DOCUMENT ...
type DOCUMENT struct {
	DOCUMENTNAME1  string `xml:"DOCUMENT_NAME_1,omitempty"`
	DOCUMENTNAME2  string `xml:"DOCUMENT_NAME_2,omitempty"`
	DOCUMENTNAME3  string `xml:"DOCUMENT_NAME_3,omitempty"`
	DOCUMENTNAME4  string `xml:"DOCUMENT_NAME_4,omitempty"`
	DOCUMENTNAME5  string `xml:"DOCUMENT_NAME_5,omitempty"`
	DOCUMENTNAME6  string `xml:"DOCUMENT_NAME_6,omitempty"`
	DOCUMENTNAME7  string `xml:"DOCUMENT_NAME_7,omitempty"`
	DOCUMENTNAME8  string `xml:"DOCUMENT_NAME_8,omitempty"`
	DOCUMENTNAME9  string `xml:"DOCUMENT_NAME_9,omitempty"`
	DOCUMENTNAME10 string `xml:"DOCUMENT_NAME_10,omitempty"`
}

// DOCUMENTS ...
type DOCUMENTS struct {
	DOCUMENT []*DOCUMENT `xml:"DOCUMENT,omitempty"`
}

// FUNDDETAIL ...
type FUNDDETAIL struct {
	XMLName                   xml.Name `xml:"FUND_DETAIL"`
	FUNDNAME1                 string   `xml:"FUND_NAME_1"`
	FUNDALLOCATIONPERCENTAGE1 uint8    `xml:"FUND_ALLOCATION_PERCENTAGE_1"`
	FUNDNAME2                 string   `xml:"FUND_NAME_2"`
	FUNDALLOCATIONPERCENTAGE2 uint8    `xml:"FUND_ALLOCATION_PERCENTAGE_2"`
	FUNDNAME3                 string   `xml:"FUND_NAME_3"`
	FUNDALLOCATIONPERCENTAGE3 uint8    `xml:"FUND_ALLOCATION_PERCENTAGE_3"`
	FUNDNAME4                 string   `xml:"FUND_NAME_4"`
	FUNDALLOCATIONPERCENTAGE4 uint8    `xml:"FUND_ALLOCATION_PERCENTAGE_4"`
}

// FUNDDETAILS ...
type FUNDDETAILS struct {
	XMLName    xml.Name      `xml:"FUND_DETAILS"`
	FUNDDETAIL []*FUNDDETAIL `xml:"FUND_DETAIL,omitempty"`
}

// DELIVERY ...
type DELIVERY struct {
	RELATEDROLEID                  string `xml:"RELATED_ROLE_ID,omitempty"`
	ROLEOPTID                      int    `xml:"ROLE_OPT_ID,omitempty"`
	PARTYDELIVERYIND               string `xml:"PARTY_DELIVERY_IND,omitempty"`
	PARTYDELIVERYTYPE              string `xml:"PARTY_DELIVERY_TYPE,omitempty"`
	PARTYDELIVERYEMAILNOTIFICATION string `xml:"PARTY_DELIVERY_EMAIL_NOTIFICATION,omitempty"`
}

// ROLE ...
type ROLE struct {
	ROLEID        string `xml:"ROLE_ID"`
	ROLECODE      string `xml:"ROLE_CODE"`
	ROLETYPE      string `xml:"ROLE_TYPE"`
	ROLENAME      string `xml:"ROLE_NAME,omitempty"`
	ROLESTATUS    string `xml:"ROLE_STATUS,omitempty"`
	RELATIONTOINS string `xml:"RELATION_TO_INS,omitempty"`
	ROLECCIND     string `xml:"ROLE_CC_IND,omitempty"`
}

// ROLES ...
type ROLES struct {
	ROLE []*ROLE `xml:"ROLE,omitempty"`
}

// ADDRESS ...
type ADDRESS struct {
	ADDRTYPE           string `xml:"ADDR_TYPE,omitempty"`
	ADDRLINE1          string `xml:"ADDR_LINE1,omitempty"`
	ADDRLINE2          string `xml:"ADDR_LINE2,omitempty"`
	ADDRLINE3          string `xml:"ADDR_LINE3,omitempty"`
	ADDRCITY           string `xml:"ADDR_CITY,omitempty"`
	ADDRSTATE          string `xml:"ADDR_STATE,omitempty"`
	ADDRZIP            string `xml:"ADDR_ZIP,omitempty"`
	ADDRZIPEXTN        string `xml:"ADDR_ZIP_EXTN,omitempty"`
	ADDRCNTRYCODE      string `xml:"ADDR_CNTRY_CODE,omitempty"`
	ADDRSTATUS         string `xml:"ADDR_STATUS,omitempty"`
	ADDRPREFIND        string `xml:"ADDR_PREF_IND,omitempty"`
	ADDRCLASSIFICATION string `xml:"ADDR_CLASSIFICATION,omitempty"`
}

// ADDRESSES ...
type ADDRESSES struct {
	ADDRESS []*ADDRESS `xml:"ADDRESS,omitempty"`
}

// EMAILADDRESS ...
type EMAILADDRESS struct {
	XMLName             xml.Name `xml:"EMAIL_ADDRESS"`
	EMAILADDR           string   `xml:"EMAIL_ADDR,omitempty"`
	EMAILTYPE           string   `xml:"EMAIL_TYPE,omitempty"`
	STATUS              string   `xml:"STATUS,omitempty"`
	EMAILPREFERREDIND   string   `xml:"EMAIL_PREFERRED_IND,omitempty"`
	EMAILCLASSIFICATION string   `xml:"EMAIL_CLASSIFICATION,omitempty"`
	PERSONAL            string   `xml:"PERSONAL,omitempty"`
	BUSINESS            string   `xml:"BUSINESS,omitempty"`
	OTHER               string   `xml:"OTHER,omitempty"`
}

// EMAILADDRESSES ...
type EMAILADDRESSES struct {
	XMLName      xml.Name        `xml:"EMAIL_ADDRESSES"`
	EMAILADDRESS []*EMAILADDRESS `xml:"EMAIL_ADDRESS,omitempty"`
}

// PARTYPHONE ...
type PARTYPHONE struct {
	XMLName           xml.Name `xml:"PARTY_PHONE"`
	PHNNUM            string   `xml:"PHN_NUM,omitempty"`
	PHNEXT            string   `xml:"PHN_EXT,omitempty"`
	PHNTYPE           string   `xml:"PHN_TYPE,omitempty"`
	STATUS            string   `xml:"STATUS,omitempty"`
	PHNPREFERREDIND   string   `xml:"PHN_PREFERRED_IND,omitempty"`
	PHNCLASSIFICATION string   `xml:"PHN_CLASSIFICATION,omitempty"`
	MOBILE            string   `xml:"MOBILE,omitempty"`
	BUSINESS          string   `xml:"BUSINESS,omitempty"`
	HOME              string   `xml:"HOME,omitempty"`
}

// PARTYPHONES ...
type PARTYPHONES struct {
	XMLName    xml.Name      `xml:"PARTY_PHONES"`
	PARTYPHONE []*PARTYPHONE `xml:"PARTY_PHONE,omitempty"`
}

// PARTY ...
type PARTY struct {
	PARTYCONT           string          `xml:"PARTY_CONT,omitempty"`
	PARTYID             string          `xml:"PARTY_ID"`
	PARTYPCT            float64         `xml:"PARTY_PCT,omitempty"`
	PARTYTAXID          string          `xml:"PARTY_TAX_ID,omitempty"`
	CHILDFULLNAME       string          `xml:"CHILD_FULL_NAME,omitempty"`
	CHILDDOB            string          `xml:"CHILD_DOB,omitempty"`
	PARTYEXTERNALID     string          `xml:"PARTY_EXTERNAL_ID,omitempty"`
	PARTYAPPROVALIND    string          `xml:"PARTY_APPROVAL_IND,omitempty"`
	DELIVERY            *DELIVERY       `xml:"DELIVERY,omitempty"`
	ROLES               *ROLES          `xml:"ROLES,omitempty"`
	PARTYTYPE           string          `xml:"PARTY_TYPE,omitempty"`
	PARTYFULLNAME       string          `xml:"PARTY_FULL_NAME,omitempty"`
	PARTYPREFIX         string          `xml:"PARTY_PREFIX,omitempty"`
	PARTYFSTNAME        string          `xml:"PARTY_FST_NAME,omitempty"`
	PARTYMI             string          `xml:"PARTY_MI,omitempty"`
	PARTYLSTNAME        string          `xml:"PARTY_LST_NAME,omitempty"`
	PARTYSUFFIX         string          `xml:"PARTY_SUFFIX,omitempty"`
	ADDRESSES           *ADDRESSES      `xml:"ADDRESSES"`
	PARTYDOB            string          `xml:"PARTY_DOB,omitempty"`
	PARTYATTAINAGE      int             `xml:"PARTY_ATTAIN_AGE,omitempty"`
	PARTYGENDER         string          `xml:"PARTY_GENDER,omitempty"`
	PARTYGENDERIDENTITY string          `xml:"PARTY_GENDER_IDENTITY,omitempty"`
	EMAILADDRESSES      *EMAILADDRESSES `xml:"EMAIL_ADDRESSES,omitempty"`
	PARTYPHONES         *PARTYPHONES    `xml:"PARTY_PHONES,omitempty"`
	PREFERRED           string          `xml:"PREFERRED,omitempty"`
}

// PARTIES ...
type PARTIES struct {
	PARTY []*PARTY `xml:"PARTY,omitempty"`
}

// CARRIERADDRESS ...
type CARRIERADDRESS struct {
	XMLName                xml.Name `xml:"CARRIER_ADDRESS"`
	CARRIERADDRLINE1       string   `xml:"CARRIER_ADDR_LINE1,omitempty"`
	CARRIERADDRLINE2       string   `xml:"CARRIER_ADDR_LINE2,omitempty"`
	CARRIERADDRLINE3       string   `xml:"CARRIER_ADDR_LINE3,omitempty"`
	CARRIERCITY            string   `xml:"CARRIER_CITY,omitempty"`
	CARRIERSTATE           string   `xml:"CARRIER_STATE,omitempty"`
	CARRIERZIP             string   `xml:"CARRIER_ZIP,omitempty"`
	CARRIERZIPEXTN         string   `xml:"CARRIER_ZIP_EXTN,omitempty"`
	CARRIERADDRCOUNTRYCODE string   `xml:"CARRIER_ADDR_COUNTRY_CODE,omitempty"`
	CARRIERADDRTYPE        string   `xml:"CARRIER_ADDR_TYPE,omitempty"`
	CARRIERADDRSTATUS      string   `xml:"CARRIER_ADDR_STATUS,omitempty"`
}

// CARRIERADDRESSES ...
type CARRIERADDRESSES struct {
	XMLName        xml.Name          `xml:"CARRIER_ADDRESSES"`
	CARRIERADDRESS []*CARRIERADDRESS `xml:"CARRIER_ADDRESS"`
}

// CARRIERPHONE ...
type CARRIERPHONE struct {
	XMLName        xml.Name `xml:"CARRIER_PHONE"`
	CARRIERPHNNUM  string   `xml:"CARRIER_PHN_NUM,omitempty"`
	CARRIERPHNTYPE string   `xml:"CARRIER_PHN_TYPE,omitempty"`
}

// CARRIERPHONES ...
type CARRIERPHONES struct {
	XMLName      xml.Name        `xml:"CARRIER_PHONES"`
	CARRIERPHONE []*CARRIERPHONE `xml:"CARRIER_PHONE"`
}

// CARRIEREMAIL ...
type CARRIEREMAIL struct {
	XMLName          xml.Name `xml:"CARRIER_EMAIL"`
	CARRIEREMAILADDR string   `xml:"CARRIER_EMAIL_ADDR,omitempty"`
	CARRIEREMAILTYPE string   `xml:"CARRIER_EMAIL_TYPE,omitempty"`
}

// CARRIEREMAILS ...
type CARRIEREMAILS struct {
	XMLName      xml.Name        `xml:"CARRIER_EMAILS"`
	CARRIEREMAIL []*CARRIEREMAIL `xml:"CARRIER_EMAIL,omitempty"`
}

// CARRIERWEBLINK ...
type CARRIERWEBLINK struct {
	XMLName            xml.Name `xml:"CARRIER_WEB_LINK"`
	CARRIERWEBLINKURL  string   `xml:"CARRIER_WEB_LINK_URL,omitempty"`
	CUSTOMERWEBLINKURL string   `xml:"CUSTOMER_WEB_LINK_URL,omitempty"`
	AGENTWEBLINKURL    string   `xml:"AGENT_WEB_LINK_URL,omitempty"`
	CARRIERWEBLINKTYPE string   `xml:"CARRIER_WEB_LINK_TYPE,omitempty"`
}

// CARRIERWEBLINKS ...
type CARRIERWEBLINKS struct {
	XMLName        xml.Name          `xml:"CARRIER_WEB_LINKS"`
	CARRIERWEBLINK []*CARRIERWEBLINK `xml:"CARRIER_WEB_LINK,omitempty"`
}

// CARRIER ...
type CARRIER struct {
	CARRIERCONT         string            `xml:"CARRIER_CONT,omitempty"`
	CARRIERID           string            `xml:"CARRIER_ID,omitempty"`
	CARRIERCODE         string            `xml:"CARRIER_CODE,omitempty"`
	CARRIERNAICCODE     string            `xml:"CARRIER_NAIC_CODE,omitempty"`
	CARRIERORGCODE      string            `xml:"CARRIER_ORG_CODE,omitempty"`
	CARRIERDISPLAYNAME  string            `xml:"CARRIER_DISPLAY_NAME,omitempty"`
	CARRIERBUSINESSNAME string            `xml:"CARRIER_BUSINESS_NAME,omitempty"`
	CARRIERADDRESSES    *CARRIERADDRESSES `xml:"CARRIER_ADDRESSES"`
	CARRIERPHONES       *CARRIERPHONES    `xml:"CARRIER_PHONES"`
	CARRIEREMAILS       *CARRIEREMAILS    `xml:"CARRIER_EMAILS,omitempty"`
	CARRIERWEBLINKS     *CARRIERWEBLINKS  `xml:"CARRIER_WEB_LINKS,omitempty"`
	CARRIEROFFICEHOURS  string            `xml:"CARRIER_OFFICE_HOURS,omitempty"`
	CARRIEROFFICEDAYS   string            `xml:"CARRIER_OFFICE_DAYS,omitempty"`
	CARRIERBUDGETCNTR   string            `xml:"CARRIER_BUDGET_CNTR,omitempty"`
}

// CARRIERINFORMATION ...
type CARRIERINFORMATION struct {
	XMLName xml.Name `xml:"CARRIER_INFORMATION"`
	CARRIER *CARRIER `xml:"CARRIER,omitempty"`
}

// EXCHANGE ...
type EXCHANGE struct {
	EXCROLECODE        string  `xml:"EXC_ROLE_CODE,omitempty"`
	EXCTRNSCOMPACCTNUM string  `xml:"EXC_TRNS_COMP_ACCT_NUM,omitempty"`
	EXCCOMPACCTNUM     string  `xml:"EXC_COMP_ACCT_NUM,omitempty"`
	EXCCOMPNAME        string  `xml:"EXC_COMP_NAME,omitempty"`
	EXCCOMPADDRLINE1   string  `xml:"EXC_COMP_ADDR_LINE1,omitempty"`
	EXCCOMPADDRLINE2   string  `xml:"EXC_COMP_ADDR_LINE2,omitempty"`
	EXCCOMPADDRLINE3   string  `xml:"EXC_COMP_ADDR_LINE3,omitempty"`
	EXCCOMPCITY        string  `xml:"EXC_COMP_CITY,omitempty"`
	EXCCOMPSTATE       string  `xml:"EXC_COMP_STATE,omitempty"`
	EXCCOMPZIP         string  `xml:"EXC_COMP_ZIP,omitempty"`
	EXCDELIVERYMODE    string  `xml:"EXC_DELIVERY_MODE,omitempty"`
	EXCFAXNUM          string  `xml:"EXC_FAX_NUM,omitempty"`
	EXCERNUM           string  `xml:"EXC_ER_NUM,omitempty"`
	EXCTOTAMT          float64 `xml:"EXC_TOT_AMT,omitempty"`
}

// EXCHANGES ...
type EXCHANGES struct {
	EXCHANGE *EXCHANGE `xml:"EXCHANGE,omitempty"`
}

// FINANCIALINFORMATION ...
type FINANCIALINFORMATION struct {
	XMLName                        xml.Name `xml:"FINANCIAL_INFORMATION"`
	FINANCIALANNUALINCOME          string   `xml:"FINANCIAL_ANNUAL_INCOME,omitempty"`
	FINANCIALANNUALEXPENSES        string   `xml:"FINANCIAL_ANNUAL_EXPENSES,omitempty"`
	FINANCIALDISPOSABLEINCOME      string   `xml:"FINANCIAL_DISPOSABLE_INCOME,omitempty"`
	FINANCIALCHANGEACKNOWLEDGEMENT string   `xml:"FINANCIAL_CHANGE_ACKNOWLEDGEMENT,omitempty"`
	FINANCIALRESIDENCETYPE         string   `xml:"FINANCIAL_RESIDENCE_TYPE,omitempty"`
	FINANCIALSUFFICIENTFUND        string   `xml:"FINANCIAL_SUFFICIENT_FUND,omitempty"`
	FINANCIALTAXBRACKET            string   `xml:"FINANCIAL_TAX_BRACKET,omitempty"`
	FINANCIALNETWORTH              string   `xml:"FINANCIAL_NET_WORTH,omitempty"`
	FINANCIALLIQUIDASSET           string   `xml:"FINANCIAL_LIQUID_ASSET,omitempty"`
	FINANCIALCABENEFIT             string   `xml:"FINANCIAL_CABENEFIT,omitempty"`
	FINANCIALPRODUCTS              string   `xml:"FINANCIAL_PRODUCTS,omitempty"`
	FINANCIALTOTALANNUITYVALUE     string   `xml:"FINANCIAL_TOTAL_ANNUITY_VALUE,omitempty"`
	FINANCIALPOTENTIALINTERESTS    string   `xml:"FINANCIAL_POTENTIAL_INTERESTS,omitempty"`
	FINANCIALSOURCEOFFUNDS         string   `xml:"FINANCIAL_SOURCE_OF_FUNDS,omitempty"`
	FINANCIALREPLACEMENTS          string   `xml:"FINANCIAL_REPLACEMENTS,omitempty"`
	FINANCIALDISTRIBUTIONS         string   `xml:"FINANCIAL_DISTRIBUTIONS,omitempty"`
	FINANCIALFIRSTDISTRIBUTION     string   `xml:"FINANCIAL_FIRST_DISTRIBUTION,omitempty"`
}

// POLICYVALUES ...
type POLICYVALUES struct {
	PVALCONT                                string `xml:"PVAL_CONT,omitempty"`
	PVALCONYEAR                             string `xml:"PVAL_CON_YEAR,omitempty"`
	PVALFREEDAYS                            string `xml:"PVAL_FREE_DAYS,omitempty"`
	PVALGUARANNUALREDUCEDPAIDUPAMOUNT       string `xml:"PVAL_GUAR_ANNUAL_REDUCED_PAID_UP_AMOUNT,omitempty"`
	PVALGUARPCT                             string `xml:"PVAL_GUAR_PCT,omitempty"`
	PVALBEGINDATE                           string `xml:"PVAL_BEGIN_DATE,omitempty"`
	PVALENDDATE                             string `xml:"PVAL_END_DATE,omitempty"`
	PVALBEGINCOVERVAL                       string `xml:"PVAL_BEGIN_COVER_VAL,omitempty"`
	PVALENDCOVERVAL                         string `xml:"PVAL_END_COVER_VAL,omitempty"`
	PVALCHGCOVERVAL                         string `xml:"PVAL_CHG_COVER_VAL,omitempty"`
	PVALBEGINDEATHBNFTVAL                   string `xml:"PVAL_BEGIN_DEATH_BNFT_VAL,omitempty"`
	PVALENDDEATHBNFTVAL                     string `xml:"PVAL_END_DEATH_BNFT_VAL,omitempty"`
	PVALCHGDEATHBNFTVAL                     string `xml:"PVAL_CHG_DEATH_BNFT_VAL,omitempty"`
	PVALBEGINSURRVAL                        string `xml:"PVAL_BEGIN_SURR_VAL,omitempty"`
	PVALENDSURRVAL                          string `xml:"PVAL_END_SURR_VAL,omitempty"`
	PVALCHGSURRVAL                          string `xml:"PVAL_CHG_SURR_VAL,omitempty"`
	PVALBEGINACCVAL                         string `xml:"PVAL_BEGIN_ACC_VAL,omitempty"`
	PVALENDACCVAL                           string `xml:"PVAL_END_ACC_VAL,omitempty"`
	PVALCHGACCVAL                           string `xml:"PVAL_CHG_ACC_VAL,omitempty"`
	PVALTOTALPAYRCVD                        string `xml:"PVAL_TOTAL_PAY_RCVD,omitempty"`
	PVALTOTALCOICOST                        string `xml:"PVAL_TOTAL_COI_COST,omitempty"`
	PVALCOSTOFINSVAL                        string `xml:"PVAL_COST_OF_INS_VAL,omitempty"`
	PVALTOTALUNITCHARGE                     string `xml:"PVAL_TOTAL_UNIT_CHARGE,omitempty"`
	PVALTOTALEXPENSECHARGE                  string `xml:"PVAL_TOTAL_EXPENSE_CHARGE,omitempty"`
	PVALTOTALPARTWITHDRAWVAL                string `xml:"PVAL_TOTAL_PART_WITHDRAW_VAL,omitempty"`
	PVALTOTALLOANAMT                        string `xml:"PVAL_TOTAL_LOAN_AMT,omitempty"`
	PVALTOTALLOANBALNAMT                    string `xml:"PVAL_TOTAL_LOAN_BALN_AMT,omitempty"`
	PVALTOTALLOANREPAYAMT                   string `xml:"PVAL_TOTAL_LOAN_REPAY_AMT,omitempty"`
	PVALTOTALINTCHARGE                      string `xml:"PVAL_TOTAL_INT_CHARGE,omitempty"`
	PVALTOTALINTCREDIT                      string `xml:"PVAL_TOTAL_INT_CREDIT,omitempty"`
	PVALINTCREDITCURRRATE                   string `xml:"PVAL_INT_CREDIT_CURR_RATE,omitempty"`
	PVALLOANINTCURRRATE                     string `xml:"PVAL_LOAN_INT_CURR_RATE,omitempty"`
	PVALLOANCREDITCURRRATE                  string `xml:"PVAL_LOAN_CREDIT_CURR_RATE,omitempty"`
	PVALINTCREDITNEXTYRRATE                 string `xml:"PVAL_INT_CREDIT_NEXT_YR_RATE,omitempty"`
	PVALLOANINTNEXTYRRATE                   string `xml:"PVAL_LOAN_INT_NEXT_YR_RATE,omitempty"`
	PVALLOANCREDITNEXTYRRATE                string `xml:"PVAL_LOAN_CREDIT_NEXT_YR_RATE,omitempty"`
	PVALCURRPAYAMT                          string `xml:"PVAL_CURR_PAY_AMT,omitempty"`
	PVALCURRPAYMODE                         string `xml:"PVAL_CURR_PAY_MODE,omitempty"`
	PVALANNLPSAMT                           string `xml:"PVAL_ANN_LPS_AMT,omitempty"`
	PVALINITIALPREMIUMREQUESTAMOUNT         string `xml:"PVAL_INITIAL_PREMIUM_REQUEST_AMOUNT"`
	PVALINITIALPAYMENTAMOUNTRECEIVEDDATE    string `xml:"PVAL_INITIAL_PAYMENT_AMOUNT_RECEIVED_DATE,omitempty"`
	PVALGUARANTEEDINTERESTRATE              string `xml:"PVAL_GUARANTEED_INTEREST_RATE,omitempty"`
	PVALFIXEDGUARANTEEDMINIINTERESTRATE     string `xml:"PVAL_FIXED_GUARANTEED_MINI_INTEREST_RATE,omitempty"`
	PVALGUARANTEEDMONTHLYEXPENSECHARGE      string `xml:"PVAL_GUARANTEED_MONTHLY_EXPENSE_CHARGE,omitempty"`
	PVALGUARANTEEDMONTHLYPOSTEXPENSECHARGE  string `xml:"PVAL_GUARANTEED_MONTHLY_POST_EXPENSE_CHARGE,omitempty"`
	PVALGUARANTEEDMONTHLYUNITCHARGERATE     string `xml:"PVAL_GUARANTEED_MONTHLY_UNIT_CHARGE_RATE,omitempty"`
	PVALGUARANTEEDMONTHLYPOSTUNITCHARGERATE string `xml:"PVAL_GUARANTEED_MONTHLY_POST_UNIT_CHARGE_RATE,omitempty"`
	PVALPAYMENTCHARGEPERCENTAGE             string `xml:"PVAL_PAYMENT_CHARGE_PERCENTAGE,omitempty"`
	PVALPAYMENTCHARGEPERCENTAGE1            string `xml:"PVAL_PAYMENT_CHARGE_PERCENTAGE_1,omitempty"`
	PVALPAYMENTCHARGEPERCENTAGE2            string `xml:"PVAL_PAYMENT_CHARGE_PERCENTAGE_2,omitempty"`
	PVALPAYMENTCHARGEPERCENTAGE3            string `xml:"PVAL_PAYMENT_CHARGE_PERCENTAGE_3,omitempty"`
	PVALMINREQUIREDPREMIUMAMOUNT            string `xml:"PVAL_MIN_REQUIRED_PREMIUM_AMOUNT,omitempty"`
	PVALINITIALPREMIUMRECEIVEDAMOUNT        string `xml:"PVAL_INITIAL_PREMIUM_RECEIVED_AMOUNT,omitempty"`
	PVALINITIALINTRATE                      string `xml:"PVAL_INITIAL_INT_RATE,omitempty"`
	WITHDRAWALMAXANNUALPERCENTAGE           string `xml:"WITHDRAWAL_MAX_ANNUAL_PERCENTAGE,omitempty"`
	GAURSUBSEQPERCENTAGE                    string `xml:"GAUR_SUBSEQ_PERCENTAGE,omitempty"`
	GAURINITIALPERCENTAGE                   string `xml:"GAUR_INITIAL_PERCENTAGE,omitempty"`
	PVALGUARMININTRATE                      string `xml:"PVAL_GUAR_MIN_INT_RATE,omitempty"`
	PVALMININITPCT                          string `xml:"PVAL_MIN_INIT_PCT,omitempty"`
	PVALMINSUBPCT                           string `xml:"PVAL_MIN_SUB_PCT,omitempty"`
	PVALGUARMINRATE                         string `xml:"PVAL_GUAR_MIN_RATE,omitempty"`
	PVALGUARSURRPERCENTAGE                  string `xml:"PVAL_GUAR_SURR_PERCENTAGE,omitempty"`
	PVALNFFORFEITURERATE                    string `xml:"PVAL_NF_FORFEITURE_RATE,omitempty"`
	PVALWITHDRAWALFREEPCT                   string `xml:"PVAL_WITHDRAWAL_FREE_PCT,omitempty"`
	PVALMVAINDEX                            string `xml:"PVAL_MVA_INDEX,omitempty"`
	PVALWITHDRAWALMINAMT                    string `xml:"PVAL_WITHDRAWAL_MIN_AMT,omitempty"`
	PVALCONTRACTMINVAL                      string `xml:"PVAL_CONTRACT_MIN_VAL,omitempty"`
	PVALSURRCHRGTERMPERC                    string `xml:"PVAL_SURR_CHRG_TERM_PERC,omitempty"`
	PVALCURRPERCENTAGE20                    string `xml:"PVAL_CURR_PERCENTAGE_20,omitempty"`
	PVALCURRPERCENTAGE21                    string `xml:"PVAL_CURR_PERCENTAGE_21,omitempty"`
	PVALCURRPERCENTAGE31                    string `xml:"PVAL_CURR_PERCENTAGE_31,omitempty"`
	PVALRIDERMINACCELEAMOUNT                string `xml:"PVAL_RIDER_MIN_ACCELE_AMOUNT,omitempty"`
	PVALLOANMAXINTERESTRATE                 string `xml:"PVAL_LOAN_MAX_INTEREST_RATE,omitempty"`
	PVALLOANMINAMT                          string `xml:"PVAL_LOAN_MIN_AMT,omitempty"`
	PVALLOANMINREPAYMENT                    string `xml:"PVAL_LOAN_MIN_REPAYMENT,omitempty"`
	PVALGUARANTEEMAXEXPENSECHARGERATE       string `xml:"PVAL_GUARANTEE_MAX_EXPENSE_CHARGE_RATE,omitempty"`
}

// SURRENDERCHARGE ...
type SURRENDERCHARGE struct {
	SURRCHRGTYPE     string `xml:"SURR_CHRG_TYPE,omitempty"`
	SURRCHRGTERMPERC string `xml:"SURR_CHRG_TERM_PERC,omitempty"`
	SURRCHRGTERMYEAR int    `xml:"SURR_CHRG_TERM_YEAR,omitempty"`
}

// SURRENDERCHARGES ...
type SURRENDERCHARGES struct {
	SURRENDERCHARGE []*SURRENDERCHARGE `xml:"SURRENDERCHARGE,omitempty"`
}

// FLATEXTRA ...
type FLATEXTRA struct {
	FLATEXTRATYPE      string `xml:"FLATEXTRA_TYPE,omitempty"`
	FLATEXTRADURATION  int    `xml:"FLATEXTRA_DURATION,omitempty"`
	FLATEXTRAAMOUNT    string `xml:"FLATEXTRA_AMOUNT,omitempty"`
	FLATEXTRARATE      string `xml:"FLATEXTRA_RATE,omitempty"`
	FLATEXTRASTARTDATE string `xml:"FLATEXTRA_START_DATE,omitempty"`
}

// PARTICIPANT ...
type PARTICIPANT struct {
	PARTYID           string     `xml:"PARTYID,omitempty"`
	SUBSTANDARDRATING string     `xml:"SUBSTANDARD_RATING,omitempty"`
	FLATEXTRA         *FLATEXTRA `xml:"FLATEXTRA,omitempty"`
}

// COVERAGEPARTICIPANTS ...
type COVERAGEPARTICIPANTS struct {
	XMLName     xml.Name       `xml:"COVERAGE_PARTICIPANTS"`
	PARTICIPANT []*PARTICIPANT `xml:"PARTICIPANT,omitempty"`
}

// COVERAGE ...
type COVERAGE struct {
	COVERAGEID              string                `xml:"COVERAGE_ID,omitempty"`
	COVERAGETYPE            string                `xml:"COVERAGE_TYPE,omitempty"`
	COVERAGENAME            string                `xml:"COVERAGE_NAME,omitempty"`
	CURRENTAMOUNT           string                `xml:"CURRENT_AMOUNT,omitempty"`
	ANNUALPREMIUM           string                `xml:"ANNUAL_PREMIUM,omitempty"`
	ORIGINALCOVERAGEAMOUNT  string                `xml:"ORIGINAL_COVERAGE_AMOUNT,omitempty"`
	MINCOVERAGEAMOUNT       string                `xml:"MIN_COVERAGE_AMOUNT,omitempty"`
	MAXCOVERAGEAMOUNT       string                `xml:"MAX_COVERAGE_AMOUNT,omitempty"`
	GROSSDEATHBENEFITAMOUNT string                `xml:"GROSS_DEATHBENEFIT_AMOUNT,omitempty"`
	LOWDEATHBENEFITAMOUNT   string                `xml:"LOW_DEATHBENEFIT_AMOUNT,omitempty"`
	COVERAGECHANGEAMOUNT    string                `xml:"COVERAGE_CHANGE_AMOUNT,omitempty"`
	COVERAGEISSUEAGE        string                `xml:"COVERAGE_ISSUE_AGE,omitempty"`
	COVERAGEEFFDATE         string                `xml:"COVERAGE_EFF_DATE,omitempty"`
	COVERAGECHANGEEFFDATE   string                `xml:"COVERAGE_CHANGE_EFF_DATE,omitempty"`
	COVERAGETERMINATIONDATE string                `xml:"COVERAGE_TERMINATION_DATE,omitempty"`
	UNITOFCOVERAGE          string                `xml:"UNIT_OF_COVERAGE,omitempty"`
	VALUEPERUNITOFCOVERAGE  string                `xml:"VALUE_PER_UNIT_OF_COVERAGE,omitempty"`
	GUIDELINESINGLEPREMIUM  string                `xml:"GUIDELINE_SINGLE_PREMIUM,omitempty"`
	GUIDELINELEVELPREMIUM   string                `xml:"GUIDELINE_LEVEL_PREMIUM,omitempty"`
	SEVENPAYPREMIUM         string                `xml:"SEVEN_PAY_PREMIUM,omitempty"`
	COVERAGEPARTICIPANTS    *COVERAGEPARTICIPANTS `xml:"COVERAGE_PARTICIPANTS,omitempty"`
}

// COVERAGES ...
type COVERAGES struct {
	TOTALCOVERAGEAMOUNT                             string    `xml:"TOTAL_COVERAGE_AMOUNT,omitempty"`
	CUMGROSSDEATHBENEFITAMOUNT                      string    `xml:"CUM_GROSS_DEATHBENEFIT_AMOUNT,omitempty"`
	NETDEATHBENEFIT                                 string    `xml:"NET_DEATH_BENEFIT,omitempty"`
	REMAININGDEATHBENEFITAMOUNT                     string    `xml:"REMAINING_DEATH_BENEFIT_AMOUNT,omitempty"`
	MINCOVERAGEAMOUNT                               string    `xml:"MIN_COVERAGE_AMOUNT,omitempty"`
	COVERAGEMAXANNUALCOVERAGECHANGEALLOWEDPERPOLICY string    `xml:"COVERAGE_MAX_ANNUAL_COVERAGE_CHANGE_ALLOWED_PER_POLICY,omitempty"`
	COVERAGEDEATHBENEFITOPTIONSTARTDATE             string    `xml:"COVERAGE_DEATH_BENEFIT_OPTION_START_DATE,omitempty"`
	COVERAGEDEATHBENEFITTESTOPTION                  string    `xml:"COVERAGE_DEATH_BENEFIT_TEST_OPTION,omitempty"`
	COVERAGEAVALDEATHBENEFITOPTION                  string    `xml:"COVERAGE_AVAL_DEATH_BENEFIT_OPTION,omitempty"`
	COVERAGERISKCLASSCHANGEALLOWEDYEAR              string    `xml:"COVERAGE_RISK_CLASS_CHANGE_ALLOWED_YEAR,omitempty"`
	COVERAGECHARGERATEMONTHLY                       string    `xml:"COVERAGE_CHARGE_RATE_MONTHLY,omitempty"`
	COVERAGEASSESTCHARGERATEMONTHLY                 string    `xml:"COVERAGE_ASSEST_CHARGE_RATE_MONTHLY,omitempty"`
	MAXCOVERAGEAMOUNT                               string    `xml:"MAX_COVERAGE_AMOUNT,omitempty"`
	COVERAGECHANGEEFFDATE                           string    `xml:"COVERAGE_CHANGE_EFF_DATE,omitempty"`
	COVERAGEBAND                                    string    `xml:"COVERAGE_BAND,omitempty"`
	MAXANNCOVCHANGEALLOWPERPOLICY                   string    `xml:"MAX_ANN_COV_CHANGE_ALLOW_PER_POLICY,omitempty"`
	MINCOVERAGEDECREASEAMOUNT                       string    `xml:"MIN_COVERAGE_DECREASE_AMOUNT,omitempty"`
	MAXCOVERAGEDECREASEAMOUNT                       uint8     `xml:"MAX_COVERAGE_DECREASE_AMOUNT,omitempty"`
	MAXAGENOCOVERAGEAMTDECREASE                     string    `xml:"MAX_AGE_NO_COVERAGE_AMT_DECREASE,omitempty"`
	COVERAGEAMTDECREASEALLOWED                      string    `xml:"COVERAGE_AMT_DECREASE_ALLOWED,omitempty"`
	MAXAGENOCOVERAGEAMTINCREASE                     uint8     `xml:"MAX_AGE_NO_COVERAGE_AMT_INCREASE,omitempty"`
	COVERAGEAMTINCREASEALLOWED                      uint8     `xml:"COVERAGE_AMT_INCREASE_ALLOWED,omitempty"`
	MINCOVERAGEINCREASEAMOUNT                       string    `xml:"MIN_COVERAGE_INCREASE_AMOUNT,omitempty"`
	MAXCOVERAGEINCREASEAMOUNT                       string    `xml:"MAX_COVERAGE_INCREASE_AMOUNT,omitempty"`
	COVERAGERISKCLASSCHANGECOUNT                    string    `xml:"COVERAGE_RISK_CLASS_CHANGE_COUNT,omitempty"`
	COVERAGE                                        *COVERAGE `xml:"COVERAGE"`
}

// POLICYCOVERAGES ...
type POLICYCOVERAGES struct {
	COVERAGES []*COVERAGES `xml:"COVERAGES,omitempty"`
}

// POLICYLOAN ...
type POLICYLOAN struct {
	PLOANCONT               string  `xml:"PLOAN_CONT,omitempty"`
	PLOANID                 string  `xml:"PLOAN_ID,omitempty"`
	PLOANPREMMODEAMT        float64 `xml:"PLOAN_PREM_MODE_AMT,omitempty"`
	PLOANDUEDATE            string  `xml:"PLOAN_DUE_DATE,omitempty"`
	PLOANPREMDUEDATE        string  `xml:"PLOAN_PREM_DUE_DATE,omitempty"`
	PLOANSURRAMT            float64 `xml:"PLOAN_SURR_AMT,omitempty"`
	PLOANCASHSURRVALUE      float64 `xml:"PLOAN_CASH_SURR_VALUE,omitempty"`
	PLOANBALANCE            float64 `xml:"PLOAN_BALANCE,omitempty"`
	LOANTOTALBALANCE        float64 `xml:"LOAN_TOTAL_BALANCE,omitempty"`
	LOANTOTALPRINCIPAL      float64 `xml:"LOAN_TOTAL_PRINCIPAL,omitempty"`
	LOANPAYOFFAMT           float64 `xml:"LOAN_PAYOFF_AMT,omitempty"`
	LOANMAXAMT              string  `xml:"LOAN_MAX_AMT,omitempty"`
	LOANMINAMT              string  `xml:"LOAN_MIN_AMT,omitempty"`
	LOANTOTALACCRUEDINT     string  `xml:"LOAN_TOTAL_ACCRUED_INT,omitempty"`
	LOANMAXINTERESTRATE     string  `xml:"LOAN_MAX_INTEREST_RATE,omitempty"`
	LOANCURRENTINTERESTRATE float64 `xml:"LOAN_CURRENT_INTEREST_RATE,omitempty"`
	LOANLASTINTDUEDATE      string  `xml:"LOAN_LAST_INT_DUEDATE,omitempty"`
	LOANTOTALNOOFLOAN       int     `xml:"LOAN_TOTAL_NO_OF_LOAN,omitempty"`
	LOANINTERESTMETHOD      string  `xml:"LOAN_INTEREST_METHOD,omitempty"`
	LOANTOTALYTDTAKEN       float64 `xml:"LOAN_TOTAL_YTD_TAKEN,omitempty"`
	LOANREPAYMENTTYPE       string  `xml:"LOAN_REPAYMENT_TYPE,omitempty"`
	LOANMINREPAYMENT        string  `xml:"LOAN_MIN_REPAYMENT,omitempty"`
	LOANCHARGEINTERESTRATE  string  `xml:"LOAN_CHARGE_INTEREST_RATE,omitempty"`
}

// POLICYLOANS ...
type POLICYLOANS struct {
	POLICYLOAN []*POLICYLOAN `xml:"POLICYLOAN,omitempty"`
}

// WITHDRAWALVALUE ...
type WITHDRAWALVALUE struct {
	WITHDRAWALTOTALAMT                      string  `xml:"WITHDRAWAL_TOTAL_AMT,omitempty"`
	WITHDRAWALFREEAMT                       string  `xml:"WITHDRAWAL_FREE_AMT,omitempty"`
	WITHDRAWALMINAMT                        string  `xml:"WITHDRAWAL_MIN_AMT,omitempty"`
	WITHDRAWALMAXAMT                        string  `xml:"WITHDRAWAL_MAX_AMT,omitempty"`
	WITHDRAWALMAXPERCENTAGE                 string  `xml:"WITHDRAWAL_MAX_PERCENTAGE,omitempty"`
	WITHDRAWALANNUALLIMITNOCOVDECREASE      float64 `xml:"WITHDRAWAL_ANNUAL_LIMIT_NO_COV_DECREASE,omitempty"`
	WITHDRAWALANNUALPERCENTAGENOCOVDECREASE string  `xml:"WITHDRAWAL_ANNUAL_PERCENTAGE_NO_COV_DECREASE,omitempty"`
	WITHDRAWALMAXREQUESTDURINGVESTINGPERIOD string  `xml:"WITHDRAWAL_MAX_REQUEST_DURING_VESTING_PERIOD,omitempty"`
	WITHDRAWALMAXREQUESTAFTERVESTINGPERIOD  int     `xml:"WITHDRAWAL_MAX_REQUEST_AFTER_VESTING_PERIOD,omitempty"`
	WITHDRAWALTAKENTOTALYTD                 string  `xml:"WITHDRAWAL_TAKEN_TOTAL_YTD,omitempty"`
	WITHDRAWALNUMBEROFWITHDRAWAL            int     `xml:"WITHDRAWAL_NUMBER_OF_WITHDRAWAL,omitempty"`
	WITHDRAWALALLOWEDSTARTDATE              string  `xml:"WITHDRAWAL_ALLOWED_START_DATE,omitempty"`
	WITHDRAWALALLOWEDSTARTYEAR              int     `xml:"WITHDRAWAL_ALLOWED_START_YEAR,omitempty"`
	WITHDRAWALMAXAGENOCOVERAGEAMTDECREASE   string  `xml:"WITHDRAWAL_MAX_AGE_NO_COVERAGE_AMT_DECREASE,omitempty"`
}

// WITHDRAWALVALUES ...
type WITHDRAWALVALUES struct {
	WITHDRAWALVALUE []*WITHDRAWALVALUE `xml:"WITHDRAWALVALUE,omitempty"`
}

// LOAN ...
type LOAN struct {
	LOANID                        string `xml:"LOAN_ID,omitempty"`
	LOANTYPE                      string `xml:"LOAN_TYPE,omitempty"`
	LOANINTERESTTYPE              string `xml:"LOAN_INTEREST_TYPE,omitempty"`
	LOANINTERESTDUE               string `xml:"LOAN_INTEREST_DUE,omitempty"`
	LOANPRINCIPAL                 string `xml:"LOAN_PRINCIPAL,omitempty"`
	LOANBALANCE                   string `xml:"LOAN_BALANCE,omitempty"`
	LOANCOLLATERALAMOUNT          string `xml:"LOAN_COLLATERAL_AMOUNT,omitempty"`
	LOANINTERESTRATE              string `xml:"LOAN_INTEREST_RATE,omitempty"`
	LOANCREDITRATE                string `xml:"LOAN_CREDIT_RATE,omitempty"`
	LOANACCRUEDINTEREST           string `xml:"LOAN_ACCRUED_INTEREST,omitempty"`
	LOANCOLLATERALACCRUEDINTEREST string `xml:"LOAN_COLLATERAL_ACCRUED_INTEREST,omitempty"`
	LOANTOTALYTDTAKEN             string `xml:"LOAN_TOTAL_YTD_TAKEN,omitempty"`
	LOANGLFUNDCODE                string `xml:"LOAN_GL_FUNDCODE,omitempty"`
	LOANSTARTDATE                 string `xml:"LOAN_START_DATE,omitempty"`
	LOANENDDATE                   string `xml:"LOAN_END_DATE,omitempty"`
}

// LOANSEGMENTS ...
type LOANSEGMENTS struct {
	LOAN *LOAN `xml:"LOAN,omitempty"`
}

// MATCH ...
type MATCH struct {
	MATCHID               string  `xml:"MATCH_ID,omitempty"`
	MATCHACCVALUE         float64 `xml:"MATCH_ACC_VALUE,omitempty"`
	MATCHCUMMPAYAMT       float64 `xml:"MATCH_CUMM_PAY_AMT,omitempty"`
	MATCHYTDVALUE         string  `xml:"MATCH_YTD_VALUE,omitempty"`
	MATCHMAXLIFEVESTAMT   string  `xml:"MATCH_MAX_LIFE_VEST_AMT,omitempty"`
	MATCHPERCENTAGE       string  `xml:"MATCH_PERCENTAGE,omitempty"`
	MATCHANNVESTAMT       float64 `xml:"MATCH_ANN_VEST_AMT,omitempty"`
	MATCHMINPAYMENT       float64 `xml:"MATCH_MIN_PAYMENT,omitempty"`
	MATCHPERIOD           string  `xml:"MATCH_PERIOD,omitempty"`
	MATCHSTARTDATE        string  `xml:"MATCH_START_DATE,omitempty"`
	MATCHENDDATE          string  `xml:"MATCH_END_DATE,omitempty"`
	MATCHVESTINGPERIOD    int     `xml:"MATCH_VESTING_PERIOD,omitempty"`
	MATCHVESTINGSTARTDATE string  `xml:"MATCH_VESTING_START_DATE,omitempty"`
	MATCHVESTINGENDDATE   string  `xml:"MATCH_VESTING_END_DATE,omitempty"`
	MATCHGLFUNDCODE       string  `xml:"MATCH_GL_FUND_CODE,omitempty"`
}

// POLICYMATCH ...
type POLICYMATCH struct {
	MATCH *MATCH `xml:"MATCH,omitempty"`
}

// POLICYPREMIUM ...
type POLICYPREMIUM struct {
	PREMIUMMODE        string  `xml:"PREMIUM_MODE,omitempty"`
	PREMIUMAMT         string  `xml:"PREMIUM_AMT,omitempty"`
	PREMIUMTYPE        string  `xml:"PREMIUM_TYPE,omitempty"`
	PREMIUMSTARTDATE   string  `xml:"PREMIUM_START_DATE,omitempty"`
	PREMIUMLASTPAYDATE string  `xml:"PREMIUM_LAST_PAY_DATE,omitempty"`
	PREMIUMNEXTPAYDATE string  `xml:"PREMIUM_NEXT_PAY_DATE,omitempty"`
	PREMIUMMINAMT      float64 `xml:"PREMIUM_MIN_AMT,omitempty"`
	PREMIUMDUEDATE     string  `xml:"PREMIUM_DUE_DATE,omitempty"`
}

// POLICYPREMIUMS ...
type POLICYPREMIUMS struct {
	POLICYPREMIUM []*POLICYPREMIUM `xml:"POLICYPREMIUM,omitempty"`
}

// PAYMENTPARTYROLE ...
type PAYMENTPARTYROLE struct {
	PAYMENTPARTYROLE  string `xml:"PAYMENT_PARTY_ROLE,omitempty"`
	PAYMENTPARTYID    string `xml:"PAYMENT_PARTY_ID,omitempty"`
	PAYMENTBANKID     string `xml:"PAYMENT_BANK_ID,omitempty"`
	PAYMENTPERCENTAGE string `xml:"PAYMENT_PERCENTAGE,omitempty"`
}

// PAYMENTPARTYDETAILS ...
type PAYMENTPARTYDETAILS struct {
	XMLName          xml.Name          `xml:"PAYMENT_PARTY_DETAILS"`
	PAYMENTPARTYROLE *PAYMENTPARTYROLE `xml:"PAYMENTPARTYROLE,omitempty"`
}

// POLICYPAYMENT ...
type POLICYPAYMENT struct {
	PAYMENTARRID              string               `xml:"PAYMENT_ARR_ID,omitempty"`
	PAYMENTARRTYPE            string               `xml:"PAYMENT_ARR_TYPE,omitempty"`
	REASON                    string               `xml:"REASON,omitempty"`
	PAYMENTTYPE               string               `xml:"PAYMENT_TYPE,omitempty"`
	PAYMENTSTATUS             string               `xml:"PAYMENT_STATUS,omitempty"`
	PAYMENTFORM               string               `xml:"PAYMENT_FORM,omitempty"`
	PAYMENTFREQUENCY          string               `xml:"PAYMENT_FREQUENCY,omitempty"`
	PAYMENTNUMBEROFOCCURRENCE int                  `xml:"PAYMENT_NUMBER_OF_OCCURRENCE,omitempty"`
	PAYMENTDISBURSEMENTTYPE   string               `xml:"PAYMENT_DISBURSEMENT_TYPE,omitempty"`
	PAYMENTAMT                float64              `xml:"PAYMENT_AMT,omitempty"`
	PAYMENTREQUESTDATE        string               `xml:"PAYMENT_REQUEST_DATE,omitempty"`
	PAYMENTBEGINDATE          string               `xml:"PAYMENT_BEGIN_DATE,omitempty"`
	PAYMENTENDDATE            string               `xml:"PAYMENT_END_DATE,omitempty"`
	PAYMENTLASTACTIVITYDATE   string               `xml:"PAYMENT_LAST_ACTIVITY_DATE,omitempty"`
	PAYMENTNEXTACTIVITYDATE   string               `xml:"PAYMENT_NEXT_ACTIVITY_DATE,omitempty"`
	PAYMENTPARTYDETAILS       *PAYMENTPARTYDETAILS `xml:"PAYMENT_PARTY_DETAILS,omitempty"`
}

// POLICYPAYMENTS ...
type POLICYPAYMENTS struct {
	POLICYPAYMENT []*POLICYPAYMENT `xml:"POLICYPAYMENT,omitempty"`
}

// FUND ...
type FUND struct {
	FNDSEGID           string  `xml:"FND_SEG_ID,omitempty"`
	FNDCONT            string  `xml:"FND_CONT,omitempty"`
	FNDACCCODE         string  `xml:"FND_ACC_CODE,omitempty"`
	FNDDIVCODE         string  `xml:"FND_DIV_CODE,omitempty"`
	FNDFUNDMKTGNAME    string  `xml:"FND_FUND_MKTG_NAME,omitempty"`
	FNDFUNDCORRNAME    string  `xml:"FND_FUND_CORR_NAME,omitempty"`
	FNDACCTYPE         string  `xml:"FND_ACC_TYPE,omitempty"`
	FNDACCSUBTYPE      string  `xml:"FND_ACC_SUB_TYPE,omitempty"`
	FNDENDBAL          string  `xml:"FND_END_BAL,omitempty"`
	FNDALLOCPERC       string  `xml:"FND_ALLOC_PERC,omitempty"`
	FNDRENEWDATE       string  `xml:"FND_RENEW_DATE,omitempty"`
	FNDFUNDSORTORDER   string  `xml:"FND_FUND_SORT_ORDER,omitempty"`
	FNDCLOSINGIND      string  `xml:"FND_CLOSING_IND,omitempty"`
	FNDMAXALLOCPERC    float64 `xml:"FND_MAX_ALLOC_PERC,omitempty"`
	FNDINTRATE         float64 `xml:"FND_INT_RATE,omitempty"`
	FNDGUARPRD         string  `xml:"FND_GUAR_PRD,omitempty"`
	FNDDEPOSITDATE     string  `xml:"FND_DEPOSIT_DATE,omitempty"`
	FNDINDCAPRATE      string  `xml:"FND_IND_CAP_RATE,omitempty"`
	FNDGUARMINCAPRATE  string  `xml:"FND_GUAR_MIN_CAP_RATE,omitempty"`
	FNDINDPARTRATE     string  `xml:"FND_IND_PART_RATE,omitempty"`
	FNDGUARMINPARTRATE string  `xml:"FND_GUAR_MIN_PART_RATE,omitempty"`
	FNDINDTRIGGER      string  `xml:"FND_IND_TRIGGER,omitempty"`
	FNDGUARMINTRIGGER  string  `xml:"FND_GUAR_MIN_TRIGGER,omitempty"`
	FNDINDEXNAME       string  `xml:"FND_INDEX_NAME,omitempty"`
	FNDGUARMININTRATE  string  `xml:"FND_GUAR_MIN_INT_RATE,omitempty"`
}

// FUNDS ...
type FUNDS struct {
	FUND []*FUND `xml:"FUND,omitempty"`
}

// WITHDRAWALPARTY ...
type WITHDRAWALPARTY struct {
	XMLName              xml.Name `xml:"WITHDRAWAL_PARTY"`
	WITHDRAWALPARTYROLE  string   `xml:"WITHDRAWAL_PARTY_ROLE,omitempty"`
	WITHDRAWALPARTYID    string   `xml:"WITHDRAWAL_PARTY_ID,omitempty"`
	WITHDRAWALBANKID     string   `xml:"WITHDRAWAL_BANK_ID,omitempty"`
	WITHDRAWALPERCENTAGE uint8    `xml:"WITHDRAWAL_PERCENTAGE,omitempty"`
}

// WITHDRAWALPARTYDETAILS ...
type WITHDRAWALPARTYDETAILS struct {
	XMLName         xml.Name         `xml:"WITHDRAWAL_PARTY_DETAILS"`
	WITHDRAWALPARTY *WITHDRAWALPARTY `xml:"WITHDRAWAL_PARTY,omitempty"`
}

// POLICYWITHDRAWAL ...
type POLICYWITHDRAWAL struct {
	WITHDRAWALARRTYPE            *WITHDRAWALARRTYPE      `xml:"WITHDRAWAL_ARR_TYPE,omitempty"`
	WITHDRAWALARRID              string                  `xml:"WITHDRAWAL_ARR_ID,omitempty"`
	WITHDRAWALSTATUS             string                  `xml:"WITHDRAWAL_STATUS,omitempty"`
	WITHDRAWALFORM               string                  `xml:"WITHDRAWAL_FORM,omitempty"`
	WITHDRAWALFREQUENCY          string                  `xml:"WITHDRAWAL_FREQUENCY,omitempty"`
	WITHDRAWALNUMBEROFOCCURRENCE int                     `xml:"WITHDRAWAL_NUMBER_OF_OCCURRENCE,omitempty"`
	WITHDRAWALDISBURSEMENTTYPE   string                  `xml:"WITHDRAWAL_DISBURSEMENT_TYPE,omitempty"`
	WITHDRAWALAMT                uint8                   `xml:"WITHDRAWAL_AMT,omitempty"`
	WITHDRAWALREQUESTDATE        string                  `xml:"WITHDRAWAL_REQUEST_DATE,omitempty"`
	WITHDRAWALBEGINDATE          string                  `xml:"WITHDRAWAL_BEGIN_DATE,omitempty"`
	WITHDRAWALENDDATE            string                  `xml:"WITHDRAWAL_END_DATE,omitempty"`
	WITHDRAWALTLASTACTIVITYDATE  string                  `xml:"WITHDRAWALT_LAST_ACTIVITY_DATE,omitempty"`
	WITHDRAWALNEXTACTIVITYDATE   string                  `xml:"WITHDRAWAL_NEXT_ACTIVITY_DATE,omitempty"`
	WITHDRAWALPARTYDETAILS       *WITHDRAWALPARTYDETAILS `xml:"WITHDRAWAL_PARTY_DETAILS,omitempty"`
}

// POLICYWITHDRAWALS ...
type POLICYWITHDRAWALS struct {
	POLICYWITHDRAWAL *POLICYWITHDRAWAL `xml:"POLICYWITHDRAWAL,omitempty"`
}

// POLICYBANK ...
type POLICYBANK struct {
	BANKID                 string `xml:"BANK_ID,omitempty"`
	BANKPARTYID            string `xml:"BANK_PARTY_ID,omitempty"`
	BANKSTARTDATE          string `xml:"BANK_START_DATE,omitempty"`
	BANKENDDATE            string `xml:"BANK_END_DATE,omitempty"`
	BANKSTATUS             string `xml:"BANK_STATUS,omitempty"`
	BANKACCTOKEN           string `xml:"BANK_ACC_TOKEN,omitempty"`
	BANKHOLDNAME           string `xml:"BANK_HOLD_NAME,omitempty"`
	BANKNAME               string `xml:"BANK_NAME,omitempty"`
	BANKACCTYPE            string `xml:"BANK_ACC_TYPE,omitempty"`
	BANKROUTNUM            string `xml:"BANK_ROUT_NUM,omitempty"`
	BANKACCNUM             string `xml:"BANK_ACC_NUM,omitempty"`
	BANKCREDITCARDNUM      string `xml:"BANK_CREDIT_CARD_NUM,omitempty"`
	BANKCREDITEXPDATE      string `xml:"BANK_CREDIT_EXP_DATE,omitempty"`
	BANKCREDITCARDTYPE     string `xml:"BANK_CREDIT_CARD_TYPE,omitempty"`
	BANKCREDITDEBITTYPE    string `xml:"BANK_CREDIT_DEBIT_TYPE,omitempty"`
	BANKINSTITUTIONNUM     string `xml:"BANK_INSTITUTION_NUM,omitempty"`
	BANKACCPURPOSE         string `xml:"BANK_ACC_PURPOSE,omitempty"`
	BANKLASTUPDATEDATETIME string `xml:"BANK_LAST_UPDATE_DATETIME,omitempty"`
}

// POLICYBANKINFO ...
type POLICYBANKINFO struct {
	POLICYBANK []*POLICYBANK `xml:"POLICYBANK,omitempty"`
}

// FUNDSEGMENT ...
type FUNDSEGMENT struct {
	XMLName                          xml.Name `xml:"FUND_SEGMENT"`
	FUNDSEGMENTID                    uint8    `xml:"FUND_SEGMENT_ID,omitempty"`
	FUNDID                           string   `xml:"FUND_ID,omitempty"`
	FUNDSEGMENTORIGINALDEPOSITAMOUNT float64  `xml:"FUND_SEGMENT_ORIGINAL_DEPOSIT_AMOUNT,omitempty"`
	FUNDSEGMENTORIGINALDEPOSITDATE   string   `xml:"FUND_SEGMENT_ORIGINAL_DEPOSIT_DATE,omitempty"`
	FUNDSEGMENTDEPOSITDATE           string   `xml:"FUND_SEGMENT_DEPOSIT_DATE,omitempty"`
	FUNDSEGMENTDEPOSITAMOUNT         float64  `xml:"FUND_SEGMENT_DEPOSIT_AMOUNT,omitempty"`
	FUNDSEGMENTCURRENTAMOUNT         float64  `xml:"FUND_SEGMENT_CURRENT_AMOUNT,omitempty"`
	FUNDSEGMENTRENEWALDATE           string   `xml:"FUND_SEGMENT_RENEWAL_DATE,omitempty"`
	FUNDSEGMENTNUMBEROFUNITS         string   `xml:"FUND_SEGMENT_NUMBER_OF_UNITS,omitempty"`
	FUNDSEGMENTSWEEPACCOUNTID        string   `xml:"FUND_SEGMENT_SWEEP_ACCOUNT_ID,omitempty"`
	FUNDSEGMENTSTARTDATE             uint8    `xml:"FUND_SEGMENT_START_DATE,omitempty"`
	FUNDSEGMENTENDDATE               string   `xml:"FUND_SEGMENT_END_DATE,omitempty"`
}

// FUNDSEGMENTS ...
type FUNDSEGMENTS struct {
	XMLName     xml.Name     `xml:"FUND_SEGMENTS"`
	FUNDSEGMENT *FUNDSEGMENT `xml:"FUND_SEGMENT,omitempty"`
}

// POLICYFUND ...
type POLICYFUND struct {
	FUNDID                             string        `xml:"FUND_ID,omitempty"`
	FUNDPRODUCTCODE                    string        `xml:"FUND_PRODUCT_CODE,omitempty"`
	FUNDNAME                           string        `xml:"FUND_NAME,omitempty"`
	FUNDACCOUNTTYPE                    string        `xml:"FUND_ACCOUNT_TYPE,omitempty"`
	FUNDINDEXNAME                      string        `xml:"FUND_INDEX_NAME,omitempty"`
	FUNDALLOCATIONPERCENTAGE           uint8         `xml:"FUND_ALLOCATION_PERCENTAGE,omitempty"`
	FUNDGLFUNDCODE                     uint8         `xml:"FUND_GL_FUND_CODE,omitempty"`
	FUNDTOTALFUNDVALUE                 float64       `xml:"FUND_TOTAL_FUND_VALUE,omitempty"`
	FUNDSTARTDATE                      string        `xml:"FUND_START_DATE,omitempty"`
	FUNDENDDATE                        string        `xml:"FUND_END_DATE,omitempty"`
	FUNDACCOUNTNAME                    string        `xml:"FUND_ACCOUNT_NAME,omitempty"`
	FUNDGUARANTEEDMINCAPRATE           float64       `xml:"FUND_GUARANTEED_MIN_CAP_RATE,omitempty"`
	FUNDGUARANTEEDMINPARTICIPATIONRATE float64       `xml:"FUND_GUARANTEED_MIN_PARTICIPATION_RATE,omitempty"`
	FUNDSEGMENTS                       *FUNDSEGMENTS `xml:"FUND_SEGMENTS,omitempty"`
	FUNDMINHOLDINGACCTRANSFERAMT       string        `xml:"FUND_MIN_HOLDING_ACC_TRANSFER_AMT,omitempty"`
	FUNDMINFUNDTRANSFERAMT             float64       `xml:"FUND_MIN_FUND_TRANSFER_AMT,omitempty"`
	FUNDMINREQUIREDACCOUNTVALUE        float64       `xml:"FUND_MIN_REQUIRED_ACCOUNT_VALUE,omitempty"`
}

// POLICYFUNDS ...
type POLICYFUNDS struct {
	POLICYFUND []*POLICYFUND `xml:"POLICYFUND,omitempty"`
}

// FUNDINFOSEGMENT ...
type FUNDINFOSEGMENT struct {
	XMLName                 xml.Name `xml:"FUND_INFO_SEGMENT"`
	FUNDID                  string   `xml:"FUND_ID,omitempty"`
	FUNDSEGMENTINITIALINDEX string   `xml:"FUND_SEGMENT_INITIAL_INDEX,omitempty"`
}

// FUNDINFOSEGMENTS ...
type FUNDINFOSEGMENTS struct {
	XMLName         xml.Name           `xml:"FUND_INFO_SEGMENTS"`
	FUNDINFOSEGMENT []*FUNDINFOSEGMENT `xml:"FUND_INFO_SEGMENT,omitempty"`
}

// POLICYFUNDINFO ...
type POLICYFUNDINFO struct {
	FUNDID                       string            `xml:"FUND_ID,omitempty"`
	FUNDNAME                     string            `xml:"FUND_NAME,omitempty"`
	FUNDINITIALPAYMENT           string            `xml:"FUND_INITIAL_PAYMENT,omitempty"`
	FUNDINFOSEGMENTS             *FUNDINFOSEGMENTS `xml:"FUND_INFO_SEGMENTS,omitempty"`
	FUNDMINHOLDINGACCTRANSFERAMT string            `xml:"FUND_MIN_HOLDING_ACC_TRANSFER_AMT,omitempty"`
	FUNDMINFUNDTRANSFERAMT       float64           `xml:"FUND_MIN_FUND_TRANSFER_AMT,omitempty"`
	FUNDMINREQUIREDACCOUNTVALUE  float64           `xml:"FUND_MIN_REQUIRED_ACCOUNT_VALUE,omitempty"`
}

// POLICYFUNDSINFO ...
type POLICYFUNDSINFO struct {
	POLICYFUNDINFO []*POLICYFUNDINFO `xml:"POLICYFUNDINFO,omitempty"`
}

// FEATUREFEE ...
type FEATUREFEE struct {
	XMLName   xml.Name `xml:"FEATURE_FEE"`
	FEAFEEAMT float64  `xml:"FEA_FEE_AMT,omitempty"`
	FEAFEEPCT float64  `xml:"FEA_FEE_PCT,omitempty"`
}

// FEATURE ...
type FEATURE struct {
	FEATURECODE                         string      `xml:"FEATURE_CODE,omitempty"`
	FEATUREEXTCODE                      string      `xml:"FEATURE_EXT_CODE,omitempty"`
	FEATURETYPE                         string      `xml:"FEATURE_TYPE,omitempty"`
	FEATURENAME                         string      `xml:"FEATURE_NAME,omitempty"`
	FEATUREGROUPID                      string      `xml:"FEATURE_GROUP_ID,omitempty"`
	FEATURESTATUS                       bool        `xml:"FEATURE_STATUS,omitempty"`
	FEATURESTARTDATE                    string      `xml:"FEATURE_START_DATE,omitempty"`
	FEATUREENDDATE                      string      `xml:"FEATURE_END_DATE,omitempty"`
	FEATUREEFFDATE                      string      `xml:"FEATURE_EFF_DATE,omitempty"`
	FEATUREAPPROVEDATE                  string      `xml:"FEATURE_APPROVE_DATE,omitempty"`
	FEATURETOTREQAMT                    float64     `xml:"FEATURE_TOT_REQ_AMT,omitempty"`
	FEATURETOTMINAMT                    float64     `xml:"FEATURE_TOT_MIN_AMT,omitempty"`
	FEATUREPAYAMT                       float64     `xml:"FEATURE_PAY_AMT,omitempty"`
	FEATURETOTPAYAMT                    float64     `xml:"FEATURE_TOT_PAY_AMT,omitempty"`
	FEATUREINDICATOR                    string      `xml:"FEATURE_INDICATOR,omitempty"`
	FEATUREPAYMENTFORM                  string      `xml:"FEATURE_PAYMENT_FORM,omitempty"`
	FEATUREPERIODLAPSEPROTECTION        int         `xml:"FEATURE_PERIOD_LAPSE_PROTECTION,omitempty"`
	FEATUREPAYMENTAMOUNTLAPSEPROTECTION float64     `xml:"FEATURE_PAYMENT_AMOUNT_LAPSE_PROTECTION,omitempty"`
	FEATUREFREQUENCY                    string      `xml:"FEATURE_FREQUENCY,omitempty"`
	FEATUREFEE                          *FEATUREFEE `xml:"FEATURE_FEE,omitempty"`
}

// FEATURES ...
type FEATURES struct {
	FEATURE []*FEATURE `xml:"FEATURE,omitempty"`
}

// CHARGE ...
type CHARGE struct {
	CHARGECOVERAGEID   string  `xml:"CHARGE_COVERAGE_ID,omitempty"`
	CHARGETYPE         string  `xml:"CHARGE_TYPE,omitempty"`
	CURRENTMONTHCHARGE float64 `xml:"CURRENT_MONTH_CHARGE,omitempty"`
	POLICYCHARGES      string  `xml:"POLICY_CHARGES,omitempty"`
}

// CHARGES ...
type CHARGES struct {
	CHARGE []*CHARGE `xml:"CHARGE,omitempty"`
}

// RIDERCHARGE ...
type RIDERCHARGE struct {
	XMLName                 xml.Name `xml:"RIDER_CHARGE"`
	RIDEREXERCISECHARGE     string   `xml:"RIDER_EXERCISE_CHARGE,omitempty"`
	RIDEREXERCISECHARGERATE string   `xml:"RIDER_EXERCISE_CHARGE_RATE,omitempty"`
}

// RIDERPARTICIPANT ...
type RIDERPARTICIPANT struct {
	XMLName                      xml.Name `xml:"RIDER_PARTICIPANT"`
	PARTICIPANTRISSCLASS         string   `xml:"PARTICIPANT_RISS_CLASS,omitempty"`
	PARTICIPANTSUBSTANDARDRATING string   `xml:"PARTICIPANT_SUBSTANDARD_RATING,omitempty"`
}

// RIDERPARTICIPANTS ...
type RIDERPARTICIPANTS struct {
	XMLName          xml.Name            `xml:"RIDER_PARTICIPANTS"`
	RIDERPARTICIPANT []*RIDERPARTICIPANT `xml:"RIDER_PARTICIPANT,omitempty"`
}

// COVERAGELAYER ...
type COVERAGELAYER struct {
	XMLName                 xml.Name `xml:"COVERAGE_LAYER"`
	COVERAGEID              string   `xml:"COVERAGE_ID,omitempty"`
	CURRENTAMOUNT           string   `xml:"CURRENT_AMOUNT,omitempty"`
	ANNUALPREMIUM           string   `xml:"ANNUAL_PREMIUM,omitempty"`
	COVERAGEEFFDATE         string   `xml:"COVERAGE_EFF_DATE,omitempty"`
	COVERAGETERMINATIONDATE string   `xml:"COVERAGE_TERMINATION_DATE,omitempty"`
}

// COVERAGELAYERS ...
type COVERAGELAYERS struct {
	XMLName       xml.Name         `xml:"COVERAGE_LAYERS"`
	COVERAGELAYER []*COVERAGELAYER `xml:"COVERAGE_LAYER,omitempty"`
}

// POLICYRIDER ...
type POLICYRIDER struct {
	RIDERTYPE                                      string             `xml:"RIDER_TYPE,omitempty"`
	RIDERCODE                                      string             `xml:"RIDER_CODE,omitempty"`
	RIDERNAME                                      string             `xml:"RIDER_NAME,omitempty"`
	RIDERELECTED                                   string             `xml:"RIDER_ELECTED,omitempty"`
	RIDEREFFECTIVEDATE                             string             `xml:"RIDER_EFFECTIVE_DATE,omitempty"`
	RIDERSTATUS                                    string             `xml:"RIDER_STATUS,omitempty"`
	RIDEREXERCISEDATE                              string             `xml:"RIDER_EXERCISE_DATE,omitempty"`
	RIDERTERMINATIONDATE                           string             `xml:"RIDER_TERMINATION_DATE,omitempty"`
	RIDERCOVERAGEID                                string             `xml:"RIDER_COVERAGE_ID,omitempty"`
	RIDERAMOUNT                                    string             `xml:"RIDER_AMOUNT,omitempty"`
	RIDERCOVERAGEAMT                               string             `xml:"RIDER_COVERAGE_AMT,omitempty"`
	RIDERCHARGE                                    *RIDERCHARGE       `xml:"RIDER_CHARGE,omitempty"`
	RIDERMINSAFEGAURDAGE                           string             `xml:"RIDER_MIN_SAFEGAURD_AGE,omitempty"`
	RIDERMINSAFEGAURDLOANPAYOFFAMTPER              string             `xml:"RIDER_MIN_SAFEGAURD_LOAN_PAYOFFAMT_PER,omitempty"`
	RIDERMINSAFEGAURDPOLICYYEAR                    string             `xml:"RIDER_MIN_SAFEGAURD_POLICY_YEAR,omitempty"`
	RIDERTERMINALHEALTHEVENT                       string             `xml:"RIDER_TERMINAL_HEALTH_EVENT,omitempty"`
	RIDERMAXACCELEBENEFITPERCENTAGE                string             `xml:"RIDER_MAX_ACCELE_BENEFIT_PERCENTAGE,omitempty"`
	RIDERMAXBENEFITAMT                             float64            `xml:"RIDER_MAX_BENEFIT_AMT,omitempty"`
	RIDERMAXCLAIMCOUNT                             string             `xml:"RIDER_MAX_CLAIM_COUNT,omitempty"`
	RIDERMAXADMINCHARGE                            string             `xml:"RIDER_MAX_ADMIN_CHARGE,omitempty"`
	RIDERMINACCELEAMOUNT                           string             `xml:"RIDER_MIN_ACCELE_AMOUNT,omitempty"`
	RIDERMAXACCELEAMOUNT                           string             `xml:"RIDER_MAX_ACCELE_AMOUNT,omitempty"`
	RIDERTIERIMAXCRITICALILLNESSBENEFITPERCENTAGE  string             `xml:"RIDER_TIER_I_MAX_CRITICAL_ILLNESS_BENEFIT_PERCENTAGE,omitempty"`
	RIDERTIERIMAXCRITICALILLNESSBENEFITAMOUNT      string             `xml:"RIDER_TIER_I_MAX_CRITICAL_ILLNESS_BENEFIT_AMOUNT,omitempty"`
	RIDERTIERIIMAXCRITICALILLNESSBENEFITPERCENTAGE string             `xml:"RIDER_TIER_II_MAX_CRITICAL_ILLNESS_BENEFIT_PERCENTAGE,omitempty"`
	RIDERTIERIIMAXCRITICALILLNESSBENEFITAMOUNT     string             `xml:"RIDER_TIER_II_MAX_CRITICAL_ILLNESS_BENEFIT_AMOUNT,omitempty"`
	RIDERSIBBONUSINTPER                            string             `xml:"RIDER_SIB_BONUS_INT_PER,omitempty"`
	RIDERSIBMAXBONUSAMOUNT                         string             `xml:"RIDER_SIB_MAX_BONUS_AMOUNT,omitempty"`
	RIDERMINBENEFITPCT                             string             `xml:"RIDER_MIN_BENEFIT_PCT,omitempty"`
	RIDERLIFEMAXPERCENTAGE                         string             `xml:"RIDER_LIFE_MAX_PERCENTAGE,omitempty"`
	RIDERLIFEMAXAMOUNT                             string             `xml:"RIDER_LIFE_MAX_AMOUNT,omitempty"`
	RIDERPARTICIPANTS                              *RIDERPARTICIPANTS `xml:"RIDER_PARTICIPANTS"`
	COVERAGELAYERS                                 *COVERAGELAYERS    `xml:"COVERAGE_LAYERS,omitempty"`
}

// POLICYRIDERS ...
type POLICYRIDERS struct {
	POLICYRIDER []*POLICYRIDER `xml:"POLICYRIDER,omitempty"`
}

// DEATHBENEFIT ...
type DEATHBENEFIT struct {
	XMLName                         xml.Name `xml:"DEATH_BENEFIT"`
	DEATHBENEFITOPTION              string   `xml:"DEATH_BENEFIT_OPTION,omitempty"`
	DEATHBENEFITOPTIONEFFECTIVEDATE string   `xml:"DEATH_BENEFIT_OPTION_EFFECTIVE_DATE,omitempty"`
}

// GUIDELINEPREMIUM ...
type GUIDELINEPREMIUM struct {
	XMLName                              xml.Name `xml:"GUIDELINE_PREMIUM"`
	GUIDELINEPREMIUMTESTDATE             string   `xml:"GUIDELINE_PREMIUM_TEST_DATE,omitempty"`
	DEFINITIONOFLIFEINSURANCE            string   `xml:"DEFINITION_OF_LIFE_INSURANCE,omitempty"`
	GUIDELINESINGLEPREMIUM               float64  `xml:"GUIDELINE_SINGLE_PREMIUM,omitempty"`
	GUIDELINELEVELPREMIUM                float64  `xml:"GUIDELINE_LEVEL_PREMIUM,omitempty"`
	AMOUNTEXCESSTOGUIDELINE              uint8    `xml:"AMOUNT_EXCESS_TO_GUIDELINE,omitempty"`
	TOTALGUIDELINELEVELPREMIUMSINCEISSUE float64  `xml:"TOTAL_GUIDELINE_LEVEL_PREMIUM_SINCE_ISSUE,omitempty"`
}

// MODIFIEDENDOWMENTCONTRACT ...
type MODIFIEDENDOWMENTCONTRACT struct {
	XMLName           xml.Name `xml:"MODIFIED_ENDOWMENT_CONTRACT"`
	MECTESTDATE       string   `xml:"MEC_TEST_DATE,omitempty"`
	AMOUNTEXCESSTOMEC uint8    `xml:"AMOUNT_EXCESS_TO_MEC,omitempty"`
	MECSTATUSDATE     string   `xml:"MEC_STATUS_DATE,omitempty"`
	MECSTATUS         string   `xml:"MEC_STATUS,omitempty"`
	SEVENPAYTESTBASIS float64  `xml:"SEVEN_PAY_TEST_BASIS,omitempty"`
	SEVENPAYPREMIUM   float64  `xml:"SEVEN_PAY_PREMIUM,omitempty"`
	SEVENPAYSTARTDATE string   `xml:"SEVEN_PAY_START_DATE,omitempty"`
	SEVENPAYPERIOD    string   `xml:"SEVEN_PAY_PERIOD,omitempty"`
	SEVENPAYLIMIT     float64  `xml:"SEVEN_PAY_LIMIT,omitempty"`
	YEARINPERIOD      uint8    `xml:"YEAR_IN_PERIOD,omitempty"`
}

// TESTVALUES ...
type TESTVALUES struct {
	XMLName                   xml.Name                   `xml:"TEST_VALUES"`
	GUIDELINEPREMIUM          *GUIDELINEPREMIUM          `xml:"GUIDELINE_PREMIUM,omitempty"`
	MODIFIEDENDOWMENTCONTRACT *MODIFIEDENDOWMENTCONTRACT `xml:"MODIFIED_ENDOWMENT_CONTRACT,omitempty"`
}

// ILLUSTRATION ...
type ILLUSTRATION struct {
	ILLYEAR                                                    string `xml:"ILL_YEAR,omitempty"`
	ILLPOLICYYEAR                                              int    `xml:"ILL_POLICY_YEAR,omitempty"`
	ILLINSUREDAGE                                              int    `xml:"ILL_INSURED_AGE,omitempty"`
	ILLOWNERAGE                                                int    `xml:"ILL_OWNER_AGE,omitempty"`
	ILLPREMIUM                                                 string `xml:"ILL_PREMIUM,omitempty"`
	ILLANNUALPREMIUM                                           string `xml:"ILL_ANNUAL_PREMIUM,omitempty"`
	ILLMINGUARCONTVAL                                          string `xml:"ILL_MIN_GUAR_CONT_VAL,omitempty"`
	ILLCURRCONTVAL                                             string `xml:"ILL_CURR_CONT_VAL,omitempty"`
	ILLSURRCHARGE                                              string `xml:"ILL_SURR_CHARGE,omitempty"`
	ILLGUARCONTWITHDRAWALVAL                                   string `xml:"ILL_GUAR_CONT_WITHDRAWAL_VAL,omitempty"`
	ILLCURRCONTWITHDRAWALVAL                                   string `xml:"ILL_CURR_CONT_WITHDRAWAL_VAL,omitempty"`
	ILLGUARIRR                                                 string `xml:"ILL_GUAR_IRR,omitempty"`
	ILLCURRIRR                                                 string `xml:"ILL_CURR_IRR,omitempty"`
	ILLGUARANTEEMAXEXPENSECHARGERATE                           string `xml:"ILL_GUARANTEE_MAX_EXPENSE_CHARGE_RATE,omitempty"`
	ILLGUARANTEEUNITRATE                                       string `xml:"ILL_GUARANTEE_UNIT_RATE,omitempty"`
	ILLGUARANTEEMINUNITCHARGE                                  string `xml:"ILL_GUARANTEE_MIN_UNIT_CHARGE,omitempty"`
	ILLGUARANTEEMAXUNITCHARGE                                  string `xml:"ILL_GUARANTEE_MAX_UNIT_CHARGE,omitempty"`
	ILLNHRATEPER                                               string `xml:"ILL_NH_RATE_PER,omitempty"`
	ILLNHANNGUARANTEED                                         string `xml:"ILL_NH_ANN_GUARANTEED,omitempty"`
	ILLNHANNILLUSTRATED                                        string `xml:"ILL_NH_ANN_ILLUSTRATED,omitempty"`
	ILLFIXMINCONTWITHDRAWALVAL                                 string `xml:"ILL_FIX_MIN_CONT_WITHDRAWAL_VAL,omitempty"`
	ILLGVMINCONTVAL                                            string `xml:"ILL_GV_MIN_CONT_VAL,omitempty"`
	ILLGVMINCONTWITHDRAWALVAL                                  string `xml:"ILL_GV_MIN_CONT_WITHDRAWAL_VAL,omitempty"`
	ILLDEFAULTVAL                                              string `xml:"ILL_DEFAULT_VAL,omitempty"`
	ILLDEATHBENEFIT                                            string `xml:"ILL_DEATH_BENEFIT,omitempty"`
	ILLCASHSURRENDERVALUE                                      string `xml:"ILL_CASHSURRENDER_VALUE,omitempty"`
	ILLLIFESURRENDERCOSTINDEX                                  string `xml:"ILL_LIFE_SURRENDER_COST_INDEX,omitempty"`
	ILLLIFENETPAYMENTCOSTINDEX                                 string `xml:"ILL_LIFE_NET_PAYMENT_COST_INDEX,omitempty"`
	ILLSAFEGAURDRIDERCHARGES                                   string `xml:"ILL_SAFEGAURD_RIDER_CHARGES,omitempty"`
	ILLSIBMINSAVINGPAYMENTS                                    string `xml:"ILL_SIB_MIN_SAVING_PAYMENTS,omitempty"`
	ILLGUARMAXCOIRATEPER                                       string `xml:"ILL_GUAR_MAX_COI_RATE_PER,omitempty"`
	ILLGUARMAXCOIRATE                                          string `xml:"ILL_GUAR_MAX_COI_RATE,omitempty"`
	ILLCORRRATE                                                string `xml:"ILL_CORR_RATE,omitempty"`
	ILLGUARANNUALPREMIUM                                       string `xml:"ILL_GUAR_ANNUAL_PREMIUM,omitempty"`
	ILLINFORCEIND                                              string `xml:"ILL_INFORCE_IND,omitempty"`
	ILLGUARANNUALACCVALUE                                      string `xml:"ILL_GUAR_ANNUAL_ACC_VALUE,omitempty"`
	ILLGUARCASHVALUE                                           string `xml:"ILL_GUAR_CASH_VALUE,omitempty"`
	ILLPREMIUMAMOUNT                                           string `xml:"ILL_PREMIUM_AMOUNT,omitempty"`
	ILLANNUALSURRCHARGE                                        string `xml:"ILL_ANNUAL_SURR_CHARGE,omitempty"`
	ILLGUARANNUALREDUCEDPAIDUPAMOUNT                           string `xml:"ILL_GUAR_ANNUAL_REDUCED_PAID_UP_AMOUNT,omitempty"`
	ILLANNUALDEATHBNFTAMOUNT                                   string `xml:"ILL_ANNUAL_DEATH_BNFT_AMOUNT,omitempty"`
	ILLGURANTEEDANNUALCOVERAGESWAIVEROFPREMIUMMODALPREM        string `xml:"ILL_GURANTEED_ANNUAL_COVERAGES_WAIVER_OF_PREMIUM_MODAL_PREM,omitempty"`
	ILLGURANTEEDANNUALCOVERAGESACCIDENTALDEATHBENEFITMODALPREM string `xml:"ILL_GURANTEED_ANNUAL_COVERAGES_ACCIDENTAL_DEATH_BENEFIT_MODAL_PREM,omitempty"`
	ILLGURANTEEDANNUALCOVERAGESCHILDRENSTERMMODALPREM          string `xml:"ILL_GURANTEED_ANNUAL_COVERAGES_CHILDRENSTERM_MODAL_PREM,omitempty"`
	ILLGURANTEEDANNUALFACEAMOUNT                               string `xml:"ILL_GURANTEED_ANNUAL_FACE_AMOUNT,omitempty"`
	ILLGURANTEEDANNUALACCIDENTALDEATHBNFTFACEAMOUNT            string `xml:"ILL_GURANTEED_ANNUAL_ACCIDENTAL_DEATH_BNFT_FACE_AMOUNT,omitempty"`
	ILLGURANTEEDANNUALCOVERAGESCHARITABLEGIVINGFACEAMOUNT      string `xml:"ILL_GURANTEED_ANNUAL_COVERAGES_CHARITABLE_GIVING_FACE_AMOUNT,omitempty"`
	ILLGURANTEEDANNUALCOVERAGESCHILDRENSTERMFACEAMOUNT         string `xml:"ILL_GURANTEED_ANNUAL_COVERAGES_CHILDRENS_TERM_FACE_AMOUNT,omitempty"`
	ILLGUARMAXMONTHLYPERCHARGERATEAGE                          string `xml:"ILL_GUAR_MAX_MONTHLY_PER_CHARGE_RATE_AGE,omitempty"`
	ILLGUARFACTORAGE                                           string `xml:"ILL_GUAR_FACTOR_AGE,omitempty"`
	ILLGUARANTEEDMAXANNUALTERMPREMIUMS                         string `xml:"ILL_GUARANTEED_MAX_ANNUAL_TERM_PREMIUMS,omitempty"`
	ILLGUARAGE                                                 string `xml:"ILL_GUAR_AGE,omitempty"`
	ILLANNUALACCOUNTVALUE                                      string `xml:"ILL_ANNUAL_ACCOUNT_VALUE,omitempty"`
	ILLGUARFACTORYEAR                                          string `xml:"ILL_GUAR_FACTOR_YEAR,omitempty"`
	ILLGUARPER                                                 string `xml:"ILL_GUAR_PER,omitempty"`
}

// ILLUSTRATIONVALUES ...
type ILLUSTRATIONVALUES struct {
	ILLMAXCHARGE                                        uint8           `xml:"ILL_MAX_CHARGE,omitempty"`
	ILLCOVERAGESTARTYEAR                                string          `xml:"ILL_COVERAGE_START_YEAR,omitempty"`
	ILLGURANTEEDANNUALFACEAMOUNTFROMLASTYEAR            string          `xml:"ILL_GURANTEED_ANNUAL_FACE_AMOUNT_FROM_LAST_YEAR,omitempty"`
	ILLANNUALACCOUNTVALUEFROMLASTYEAR                   string          `xml:"ILL_ANNUAL_ACCOUNT_VALUE_FROM_LAST_YEAR,omitempty"`
	ILLGUARNTEEDANNUALCOVERAGESBASEMODALPREMIUMLASTYEAR string          `xml:"ILL_GUARNTEED_ANNUAL_COVERAGES_BASE_MODALPREMIUM_LAST_YEAR,omitempty"`
	ILLLAPSEYEAR                                        string          `xml:"ILL_LAPSE_YEAR,omitempty"`
	ILLGURANTEEDANNUALCOVERAGESBASEMODALPREMIUM         string          `xml:"ILL_GURANTEED_ANNUAL_COVERAGES_BASE_MODALPREMIUM,omitempty"`
	ILLUSTRATION                                        []*ILLUSTRATION `xml:"ILLUSTRATION,omitempty"`
}

// TRANAMOUNTS ...
type TRANAMOUNTS struct {
	XMLName                     xml.Name `xml:"TRAN_AMOUNTS"`
	PAYMENTAMOUNT               float64  `xml:"PAYMENT_AMOUNT,omitempty"`
	REQUESTEDAMOUNT             float64  `xml:"REQUESTED_AMOUNT,omitempty"`
	APPLIEDAMOUNT               float64  `xml:"APPLIED_AMOUNT,omitempty"`
	CALCULATEDAMOUNT            float64  `xml:"CALCULATED_AMOUNT,omitempty"`
	CHARGEAMOUNT                float64  `xml:"CHARGE_AMOUNT,omitempty"`
	TAXABLEAMOUNT               float64  `xml:"TAXABLE_AMOUNT,omitempty"`
	AMOUNTTYPE                  string   `xml:"AMOUNT_TYPE,omitempty"`
	REQUESTEDFUNDTRANSFERAMOUNT string   `xml:"REQUESTED_FUNDTRANSFER_AMOUNT,omitempty"`
}

// FUNDDISTRIBUTIONSEGMENTS ...
type FUNDDISTRIBUTIONSEGMENTS struct {
	XMLName         xml.Name `xml:"FUND_DISTRIBUTION_SEGMENTS"`
	SEGMENTID       string   `xml:"SEGMENT_ID,omitempty"`
	CURRENTAMOUNT   uint8    `xml:"CURRENT_AMOUNT,omitempty"`
	REQUESTEDAMOUNT uint8    `xml:"REQUESTED_AMOUNT,omitempty"`
}

// DISTRIBUTION ...
type DISTRIBUTION struct {
	FUNDID                   string                    `xml:"FUND_ID,omitempty"`
	FUNDNAME                 string                    `xml:"FUND_NAME,omitempty"`
	TOTALFUNDVALUE           uint8                     `xml:"TOTAL_FUND_VALUE,omitempty"`
	REQUESTEDAMOUNT          uint8                     `xml:"REQUESTED_AMOUNT,omitempty"`
	FUNDDISTRIBUTIONSEGMENTS *FUNDDISTRIBUTIONSEGMENTS `xml:"FUND_DISTRIBUTION_SEGMENTS,omitempty"`
}

// FUNDDISTRIBUTIONS ...
type FUNDDISTRIBUTIONS struct {
	XMLName      xml.Name      `xml:"FUND_DISTRIBUTIONS"`
	DISTRIBUTION *DISTRIBUTION `xml:"DISTRIBUTION,omitempty"`
}

// SEGMENTACTIVITY ...
type SEGMENTACTIVITY struct {
	XMLName       xml.Name `xml:"SEGMENT_ACTIVITY"`
	SEGMENTID     string   `xml:"SEGMENT_ID,omitempty"`
	APPLIEDRATE   uint8    `xml:"APPLIED_RATE,omitempty"`
	APPLIEDAMOUNT uint8    `xml:"APPLIED_AMOUNT,omitempty"`
}

// FUNDACTIVITYSEGMENTS ...
type FUNDACTIVITYSEGMENTS struct {
	XMLName         xml.Name         `xml:"FUND_ACTIVITY_SEGMENTS"`
	SEGMENTID       string           `xml:"SEGMENT_ID,omitempty"`
	CURRENTAMOUNT   uint8            `xml:"CURRENT_AMOUNT,omitempty"`
	SEGMENTACTIVITY *SEGMENTACTIVITY `xml:"SEGMENT_ACTIVITY,omitempty"`
}

// ACTIVITY ...
type ACTIVITY struct {
	FUNDID               string                `xml:"FUND_ID,omitempty"`
	TOTALFUNDVALUE       uint8                 `xml:"TOTAL_FUND_VALUE,omitempty"`
	FUNDACTIVITYSEGMENTS *FUNDACTIVITYSEGMENTS `xml:"FUND_ACTIVITY_SEGMENTS,omitempty"`
}

// FUNDACTIVITIES ...
type FUNDACTIVITIES struct {
	XMLName  xml.Name  `xml:"FUND_ACTIVITIES"`
	ACTIVITY *ACTIVITY `xml:"ACTIVITY,omitempty"`
}

// LOANSEGMENTS2 ...
type LOANSEGMENTS2 struct {
	XMLName             xml.Name         `xml:"LOAN_SEGMENTS"`
	SEGMENTID           string           `xml:"SEGMENT_ID,omitempty"`
	LOANBALANCE         string           `xml:"LOAN_BALANCE,omitempty"`
	LOANACCRUEDINTEREST string           `xml:"LOAN_ACCRUED_INTEREST,omitempty"`
	SEGMENTACTIVITY     *SEGMENTACTIVITY `xml:"SEGMENT_ACTIVITY,omitempty"`
}

// LOANACTIVTY ...
type LOANACTIVTY struct {
	XMLName                  xml.Name      `xml:"LOAN_ACTIVTY"`
	TOTALLOANBALANCE         string        `xml:"TOTAL_LOAN_BALANCE,omitempty"`
	TOTALLOANACCRUEDINTEREST string        `xml:"TOTAL_LOAN_ACCRUED_INTEREST,omitempty"`
	LOANSEGMENTS             *LOANSEGMENTS `xml:"LOAN_SEGMENTS,omitempty"`
}

// LOANACTIVITIES ...
type LOANACTIVITIES struct {
	XMLName     xml.Name     `xml:"LOAN_ACTIVITIES"`
	LOANACTIVTY *LOANACTIVTY `xml:"LOAN_ACTIVTY,omitempty"`
}

// TRANPARTY ...
type TRANPARTY struct {
	XMLName            xml.Name `xml:"TRAN_PARTY"`
	PARTYROLE          string   `xml:"PARTY_ROLE,omitempty"`
	PARTYID            string   `xml:"PARTY_ID,omitempty"`
	PERCENTAGE         uint8    `xml:"PERCENTAGE,omitempty"`
	BANKID             string   `xml:"BANK_ID,omitempty"`
	PAYMENTFORM        string   `xml:"PAYMENT_FORM,omitempty"`
	DISBURSEMENTAMOUNT string   `xml:"DISBURSEMENT_AMOUNT,omitempty"`
	GROSSAMOUNT        string   `xml:"GROSS_AMOUNT,omitempty"`
}

// TAXWITHHOLDINGINSTRUCTIONS ...
type TAXWITHHOLDINGINSTRUCTIONS struct {
	XMLName            xml.Name `xml:"TAX_WITHHOLDING_INSTRUCTIONS"`
	PARTYROLE          string   `xml:"PARTY_ROLE,omitempty"`
	PARTYID            string   `xml:"PARTY_ID,omitempty"`
	TAXWITHHOLDINGTYPE string   `xml:"TAX_WITHHOLDING_TYPE,omitempty"`
	TAXRATETOUSE       string   `xml:"TAX_RATE_TO_USE,omitempty"`
	FILINGSTATUS       string   `xml:"FILING_STATUS,omitempty"`
	DOLLAR             uint8    `xml:"DOLLAR,omitempty"`
	PERCENTAGE         uint8    `xml:"PERCENTAGE,omitempty"`
	EXEMPTIONS         uint8    `xml:"EXEMPTIONS,omitempty"`
	TAXJURISDICTION    string   `xml:"TAX_JURISDICTION,omitempty"`
}

// TAXWITHHELDAMOUNTS ...
type TAXWITHHELDAMOUNTS struct {
	XMLName               xml.Name `xml:"TAX_WITHHELD_AMOUNTS"`
	PARTYROLE             string   `xml:"PARTY_ROLE,omitempty"`
	PARTYID               string   `xml:"PARTY_ID,omitempty"`
	TAXWITHHOLDINGTYPE    string   `xml:"TAX_WITHHOLDING_TYPE,omitempty"`
	WITHHELDAMOUNT        uint8    `xml:"WITHHELD_AMOUNT,omitempty"`
	WITHHELDTAXABLEAMOUNT uint8    `xml:"WITHHELD_TAXABLE_AMOUNT,omitempty"`
	APPLIEDTAXRATE        uint8    `xml:"APPLIED_TAX_RATE,omitempty"`
}

// POLICYTRANSACTION ...
type POLICYTRANSACTION struct {
	TRANID                     string                      `xml:"TRAN_ID,omitempty"`
	TRANTYPE                   string                      `xml:"TRAN_TYPE,omitempty"`
	TRANARRID                  string                      `xml:"TRAN_ARR_ID,omitempty"`
	TRANREQUESTDATE            string                      `xml:"TRAN_REQUEST_DATE,omitempty"`
	TRANEFFECTIVEDATE          string                      `xml:"TRAN_EFFECTIVE_DATE,omitempty"`
	TRANPROCESSDATE            string                      `xml:"TRAN_PROCESS_DATE,omitempty"`
	TRANSTATUS                 string                      `xml:"TRAN_STATUS,omitempty"`
	TRANACTION                 string                      `xml:"TRAN_ACTION,omitempty"`
	TRANREASON                 string                      `xml:"TRAN_REASON,omitempty"`
	TRANAMOUNTS                *TRANAMOUNTS                `xml:"TRAN_AMOUNTS,omitempty"`
	PAYORROLE                  string                      `xml:"PAYOR_ROLE,omitempty"`
	PAYORPARTYID               string                      `xml:"PAYOR_PARTY_ID,omitempty"`
	PAYORPAYMENTFORM           string                      `xml:"PAYOR_PAYMENT_FORM,omitempty"`
	PAYORBANKID                string                      `xml:"PAYOR_BANK_ID,omitempty"`
	PAYEEROLE                  string                      `xml:"PAYEE_ROLE,omitempty"`
	PAYEEPARTYID               string                      `xml:"PAYEE_PARTY_ID,omitempty"`
	PAYEEPAYMENTFORM           string                      `xml:"PAYEE_PAYMENT_FORM,omitempty"`
	PAYEEBANKID                string                      `xml:"PAYEE_BANK_ID,omitempty"`
	FUNDDISTRIBUTIONS          *FUNDDISTRIBUTIONS          `xml:"FUND_DISTRIBUTIONS,omitempty"`
	FUNDACTIVITIES             *FUNDACTIVITIES             `xml:"FUND_ACTIVITIES,omitempty"`
	LOANACTIVITIES             *LOANACTIVITIES             `xml:"LOAN_ACTIVITIES,omitempty"`
	TRANPARTY                  *TRANPARTY                  `xml:"TRAN_PARTY,omitempty"`
	TAXWITHHOLDINGINSTRUCTIONS *TAXWITHHOLDINGINSTRUCTIONS `xml:"TAX_WITHHOLDING_INSTRUCTIONS,omitempty"`
	TAXWITHHELDAMOUNTS         *TAXWITHHELDAMOUNTS         `xml:"TAX_WITHHELD_AMOUNTS,omitempty"`
}

// POLICYTRANSACTIONS ...
type POLICYTRANSACTIONS struct {
	POLICYTRANSACTION []*POLICYTRANSACTION `xml:"POLICYTRANSACTION,omitempty"`
}

// TRANSACTIONDETAIL ...
type TRANSACTIONDETAIL struct {
	TXDCONT         string  `xml:"TXD_CONT,omitempty"`
	TXDACCCODE      string  `xml:"TXD_ACC_CODE,omitempty"`
	TXDDIVCODE      string  `xml:"TXD_DIV_CODE,omitempty"`
	TXDTRANSNUM     string  `xml:"TXD_TRANS_NUM,omitempty"`
	TXDTRANSTYPE    string  `xml:"TXD_TRANS_TYPE,omitempty"`
	TXDTRANSDESC    string  `xml:"TXD_TRANS_DESC,omitempty"`
	TXDFUNDDIVNAME  string  `xml:"TXD_FUND_DIV_NAME,omitempty"`
	TXDFUNDMKTGNAME string  `xml:"TXD_FUND_MKTG_NAME,omitempty"`
	TXDTRANSAMT     float64 `xml:"TXD_TRANS_AMT,omitempty"`
	TXDTRANSDATE    string  `xml:"TXD_TRANS_DATE,omitempty"`
	TXDUNITVALUE    float64 `xml:"TXD_UNIT_VALUE,omitempty"`
	TXDTXNUNITS     string  `xml:"TXD_TXN_UNITS,omitempty"`
	TXDBEGUNITS     string  `xml:"TXD_BEG_UNITS,omitempty"`
	TXDENDUNITS     string  `xml:"TXD_END_UNITS,omitempty"`
	TXNFUNDNAME     string  `xml:"TXN_FUND_NAME,omitempty"`
}

// TRANSACTIONDETAILS ...
type TRANSACTIONDETAILS struct {
	TRANSACTIONDETAIL []*TRANSACTIONDETAIL `xml:"TRANSACTIONDETAIL,omitempty"`
}

// TRANSACTIONSUMMARY ...
type TRANSACTIONSUMMARY struct {
	TXNCONT            string              `xml:"TXN_CONT,omitempty"`
	TXNTRANSNUM        string              `xml:"TXN_TRANS_NUM,omitempty"`
	TXNTRANSDATE       string              `xml:"TXN_TRANS_DATE,omitempty"`
	TXNTRANSTYPE       string              `xml:"TXN_TRANS_TYPE,omitempty"`
	TXNTRANSDESC       string              `xml:"TXN_TRANS_DESC,omitempty"`
	TXNTRANSSTATUS     string              `xml:"TXN_TRANS_STATUS,omitempty"`
	TXNTOTALAMT        float64             `xml:"TXN_TOTAL_AMT,omitempty"`
	TXNFEDTAXWITHD     float64             `xml:"TXN_FED_TAX_WITHD,omitempty"`
	TXNSTATETAXWITHD   float64             `xml:"TXN_STATE_TAX_WITHD,omitempty"`
	TXNBKUPTAXWITHD    float64             `xml:"TXN_BKUP_TAX_WITHD,omitempty"`
	TXNNRATAXWITHD     float64             `xml:"TXN_NRA_TAX_WITHD,omitempty"`
	TRANSACTIONDETAILS *TRANSACTIONDETAILS `xml:"TRANSACTIONDETAILS,omitempty"`
}

// TRANSACTIONSUMMARIES ...
type TRANSACTIONSUMMARIES struct {
	TRANSACTIONSUMMARY []*TRANSACTIONSUMMARY `xml:"TRANSACTIONSUMMARY,omitempty"`
}

// VENDOR ...
type VENDOR struct {
	VENDORBUSINESSNAME string `xml:"VENDOR_BUSINESS_NAME,omitempty"`
	VENDORADDRLINE1    string `xml:"VENDOR_ADDR_LINE1,omitempty"`
	VENDORADDRLINE2    string `xml:"VENDOR_ADDR_LINE2,omitempty"`
	VENDORADDRLINE3    string `xml:"VENDOR_ADDR_LINE3,omitempty"`
	VENDORCITY         string `xml:"VENDOR_CITY,omitempty"`
	VENDORSTATE        string `xml:"VENDOR_STATE,omitempty"`
	VENDORZIP          string `xml:"VENDOR_ZIP,omitempty"`
	VENDORPHNNUM       string `xml:"VENDOR_PHN_NUM,omitempty"`
	VENDOREMAILADDR    string `xml:"VENDOR_EMAIL_ADDR,omitempty"`
	VENDORWEBLINKURL   string `xml:"VENDOR_WEB_LINK_URL,omitempty"`
	VENDORREASONCODE   string `xml:"VENDOR_REASON_CODE,omitempty"`
}

// VENDORS ...
type VENDORS struct {
	VENDOR []*VENDOR `xml:"VENDOR,omitempty"`
}

// REASONS ...
type REASONS struct {
	REASON1  string `xml:"REASON1,omitempty"`
	REASON2  string `xml:"REASON2,omitempty"`
	REASON3  string `xml:"REASON3,omitempty"`
	REASON4  string `xml:"REASON4,omitempty"`
	REASON5  string `xml:"REASON5,omitempty"`
	REASON6  string `xml:"REASON6,omitempty"`
	REASON7  string `xml:"REASON7,omitempty"`
	REASON8  string `xml:"REASON8,omitempty"`
	REASON9  string `xml:"REASON9,omitempty"`
	REASON10 string `xml:"REASON10,omitempty"`
}

// ENTITY ...
type ENTITY struct {
	ROLETYPE string   `xml:"ROLETYPE,omitempty"`
	REASONS  *REASONS `xml:"REASONS,omitempty"`
}

// NIGODETAILS ...
type NIGODETAILS struct {
	XMLName xml.Name `xml:"NIGO_DETAILS"`
	ENTITY  []string `xml:"ENTITY,omitempty"`
}

// POLICY ...
type POLICY struct {
	RECTYPE                        string                `xml:"REC_TYPE,omitempty"`
	POLROLETYPE                    string                `xml:"POL_ROLE_TYPE,omitempty"`
	POLDISCOUNTINDICATOR           string                `xml:"POL_DISCOUNT_INDICATOR,omitempty"`
	POLFULLNAME                    string                `xml:"POL_FULL_NAME,omitempty"`
	POLTRACKINGID                  string                `xml:"POL_TRACKING_ID,omitempty"`
	POLEXTERNALTRACKINGID          string                `xml:"POL_EXTERNAL_TRACKING_ID,omitempty"`
	POLSYSCODE                     string                `xml:"POL_SYS_CODE,omitempty"`
	POLSOURCE                      string                `xml:"POL_SOURCE,omitempty"`
	POLSERVICENAME                 string                `xml:"POL_SERVICE_NAME,omitempty"`
	POLSERVICEDESC                 string                `xml:"POL_SERVICE_DESC,omitempty"`
	POLDOCTYPE                     string                `xml:"POL_DOC_TYPE,omitempty"`
	POLCONT                        string                `xml:"POL_CONT,omitempty"`
	POLPOLNUM                      string                `xml:"POL_POL_NUM,omitempty"`
	POLCASEID                      string                `xml:"POL_CASE_ID,omitempty"`
	POLPRODCODE                    string                `xml:"POL_PROD_CODE,omitempty"`
	POLLOB                         string                `xml:"POL_LOB,omitempty"`
	POLQUALTYPE                    string                `xml:"POL_QUAL_TYPE,omitempty"`
	POLQUALDESC                    string                `xml:"POL_QUAL_DESC,omitempty"`
	POLPLANCODE                    string                `xml:"POL_PLAN_CODE,omitempty"`
	POLPLANTYPE                    string                `xml:"POL_PLAN_TYPE,omitempty"`
	POLPRODUCT                     string                `xml:"POL_PRODUCT,omitempty"`
	POLTXNDESC                     string                `xml:"POL_TXN_DESC,omitempty"`
	POLTXNAMT                      float64               `xml:"POL_TXN_AMT,omitempty"`
	POLPRODSHORTNAME               string                `xml:"POL_PROD_SHORT_NAME,omitempty"`
	POLPRDCTMKTGNAME               string                `xml:"POL_PRDCT_MKTG_NAME,omitempty"`
	POLMKTGNAME                    string                `xml:"POL_MKTG_NAME,omitempty"`
	POLPRODUCTTYPE                 string                `xml:"POL_PRODUCT_TYPE,omitempty"`
	POLCOMPNAME                    string                `xml:"POL_COMP_NAME,omitempty"`
	POLPRDCTCOMP                   string                `xml:"POL_PRDCT_COMP,omitempty"`
	POLPRCSGCOMP                   string                `xml:"POL_PRCSG_COMP,omitempty"`
	POLISSUESTATE                  string                `xml:"POL_ISSUE_STATE,omitempty"`
	POLRESIDENCESTATE              string                `xml:"POL_RESIDENCE_STATE,omitempty"`
	POLISSUETYPE                   string                `xml:"POL_ISSUE_TYPE,omitempty"`
	POLTERM                        int                   `xml:"POL_TERM,omitempty"`
	POLSTATUS                      string                `xml:"POL_STATUS,omitempty"`
	POLPREMAMT                     float64               `xml:"POL_PREM_AMT,omitempty"`
	POLPREMMODE                    string                `xml:"POL_PREM_MODE,omitempty"`
	POLPAYMENTFREQUENCY            string                `xml:"POL_PAYMENT_FREQUENCY,omitempty"`
	POLISSUEAGE                    int                   `xml:"POL_ISSUE_AGE,omitempty"`
	POLCONTAGE                     string                `xml:"POL_CONT_AGE,omitempty"`
	POLRISKCLASS                   string                `xml:"POL_RISK_CLASS,omitempty"`
	POLEFFECTIVEDATE               string                `xml:"POL_EFFECTIVE_DATE,omitempty"`
	POLISSUEDATE                   string                `xml:"POL_ISSUE_DATE,omitempty"`
	POLSTARTDATE                   string                `xml:"POL_START_DATE,omitempty"`
	POLMATURITYDATE                string                `xml:"POL_MATURITY_DATE,omitempty"`
	POLTERMDATE                    string                `xml:"POL_TERM_DATE,omitempty"`
	POLASOFDATE                    string                `xml:"POL_AS_OF_DATE,omitempty"`
	POLCURRANNIVBEGINDATE          string                `xml:"POL_CURR_ANNIV_BEGIN_DATE,omitempty"`
	POLCURRANNIVENDDATE            string                `xml:"POL_CURR_ANNIV_END_DATE,omitempty"`
	POLLASTANNIVDATE               string                `xml:"POL_LAST_ANNIV_DATE,omitempty"`
	POLCURRANNIVDATE               string                `xml:"POL_CURR_ANNIV_DATE,omitempty"`
	POLNEXTANNIVDATE               string                `xml:"POL_NEXT_ANNIV_DATE,omitempty"`
	POLNEXTANNIVBEGINDATE          string                `xml:"POL_NEXT_ANNIV_BEGIN_DATE,omitempty"`
	POLNEXTANNIVENDDATE            string                `xml:"POL_NEXT_ANNIV_END_DATE,omitempty"`
	POLFACEVALUE                   int                   `xml:"POL_FACE_VALUE,omitempty"`
	POLCYCLEDATE                   string                `xml:"POL_CYCLE_DATE,omitempty"`
	POLCURRYR                      int                   `xml:"POL_CURR_YR,omitempty"`
	POLBONUSSTARTDATE              uint8                 `xml:"POL_BONUS_START_DATE,omitempty"`
	POLGUARANTEEDINTERESTRATEYEAR  uint8                 `xml:"POL_GUARANTEED_INTEREST_RATE_YEAR,omitempty"`
	POLOWNERSAMEASINS              string                `xml:"POL_OWNER_SAME_AS_INS,omitempty"`
	POLINFORCEASVIND               string                `xml:"POL_INFORCE_ASV_IND,omitempty"`
	POLCOSTBASIS                   float64               `xml:"POL_COST_BASIS,omitempty"`
	POLISMEC                       bool                  `xml:"POL_IS_MEC,omitempty"`
	POLSURRENDERDATE               string                `xml:"POL_SURRENDER_DATE,omitempty"`
	POLFIXEDCOSTENDPERIOD          uint8                 `xml:"POL_FIXED_COST_END_PERIOD,omitempty"`
	POLFIXEDCOSTPOSTSTARTPERIOD    string                `xml:"POL_FIXED_COST_POST_START_PERIOD,omitempty"`
	POLMONTHLYDEDUCTIONDATE        string                `xml:"POL_MONTHLY_DEDUCTION_DATE,omitempty"`
	POLACCOUNTVALUE                string                `xml:"POL_ACCOUNT_VALUE,omitempty"`
	POLWITHHOLDINGAMOUNT           string                `xml:"POL_WITHHOLDING_AMOUNT,omitempty"`
	POLAPIMAGEPATH                 string                `xml:"POL_AP_IMAGE_PATH,omitempty"`
	POLAPIMAGE                     string                `xml:"POL_AP_IMAGE,omitempty"`
	POLSIGNEDAPP                   string                `xml:"POL_SIGNED_APP,omitempty"`
	POLSIGNEDOVERFLOWAPP           string                `xml:"POL_SIGNED_OVERFLOW_APP,omitempty"`
	POLSIGNEDSUPPAPP               string                `xml:"POL_SIGNED_SUPP_APP,omitempty"`
	POLSIGNEDAMENDMENTFORM         string                `xml:"POL_SIGNED_AMENDMENT_FORM,omitempty"`
	POLSIGNEDEFTFORM               string                `xml:"POL_SIGNED_EFT_FORM,omitempty"`
	POLSIGNEDABRDISCLOSURE         string                `xml:"POL_SIGNED_ABR_DISCLOSURE,omitempty"`
	POLREPLACMENTFORM              string                `xml:"POL_REPLACMENT_FORM,omitempty"`
	POLHIPAAFORM                   string                `xml:"POL_HIPAA_FORM,omitempty"`
	POLCONSENTFORM                 string                `xml:"POL_CONSENT_FORM,omitempty"`
	POLSIGNEDILLUSTRATION          string                `xml:"POL_SIGNED_ILLUSTRATION,omitempty"`
	POLSURRENDERPERIODOPT          string                `xml:"POL_SURRENDER_PERIOD_OPT,omitempty"`
	POLMIBPOSTNOTICEDISC           string                `xml:"POL_MIB_POST_NOTICE_DISC,omitempty"`
	POLANNSTARTDATE                string                `xml:"POL_ANN_START_DATE,omitempty"`
	POLTAXYEAR                     string                `xml:"POL_TAX_YEAR,omitempty"`
	POLTAXDOCUMENTS                string                `xml:"POL_TAX_DOCUMENTS,omitempty"`
	POLAPPLICATIONID               string                `xml:"POL_APPLICATION_ID,omitempty"`
	POLTERMINATIONREASONCODE       string                `xml:"POL_TERMINATION_REASON_CODE,omitempty"`
	POLENDOWMENTDATE               string                `xml:"POL_ENDOWMENT_DATE,omitempty"`
	POLENDOWMENTAMOUNT             string                `xml:"POL_ENDOWMENT_AMOUNT,omitempty"`
	POLENDOWMENTFACTOR             string                `xml:"POL_ENDOWMENT_FACTOR,omitempty"`
	POLINITIALPREMIUMAPPLIEDAMOUNT string                `xml:"POL_INITIAL_PREMIUM_APPLIED_AMOUNT,omitempty"`
	POLANNUALTARGETPREMIUM         string                `xml:"POL_ANNUAL_TARGET_PREMIUM,omitempty"`
	POLFIXEDCOSTDELIVERYENDPERIOD  string                `xml:"POL_FIXED_COST_DELIVERY_END_PERIOD,omitempty"`
	POLTARGETROLE                  string                `xml:"POL_TARGET_ROLE,omitempty"`
	POLAMENDMENTS                  *POLAMENDMENTS        `xml:"POL_AMENDMENTS,omitempty"`
	POLCOSTBASISAMOUNTBEFORE       string                `xml:"POL_COST_BASIS_AMOUNT_BEFORE,omitempty"`
	POLCOSTBASISAMOUNTAFTER        string                `xml:"POL_COST_BASIS_AMOUNT_AFTER,omitempty"`
	DOCUMENTS                      *DOCUMENTS            `xml:"DOCUMENTS,omitempty"`
	FUNDDETAILS                    *FUNDDETAILS          `xml:"FUND_DETAILS,omitempty"`
	PARTIES                        *PARTIES              `xml:"PARTIES,omitempty"`
	CARRIER                        *CARRIER              `xml:"CARRIER,omitempty"`
	CARRIERINFORMATION             *CARRIERINFORMATION   `xml:"CARRIER_INFORMATION,omitempty"`
	EXCHANGES                      *EXCHANGES            `xml:"EXCHANGES,omitempty"`
	FINANCIALINFORMATION           *FINANCIALINFORMATION `xml:"FINANCIAL_INFORMATION,omitempty"`
	POLICYVALUES                   *POLICYVALUES         `xml:"POLICYVALUES,omitempty"`
	SURRENDERCHARGES               *SURRENDERCHARGES     `xml:"SURRENDERCHARGES,omitempty"`
	POLICYCOVERAGES                *POLICYCOVERAGES      `xml:"POLICYCOVERAGES,omitempty"`
	POLICYLOANS                    *POLICYLOANS          `xml:"POLICYLOANS,omitempty"`
	WITHDRAWALVALUES               *WITHDRAWALVALUES     `xml:"WITHDRAWALVALUES,omitempty"`
	LOANSEGMENTS                   *LOANSEGMENTS         `xml:"LOANSEGMENTS,omitempty"`
	POLICYMATCH                    *POLICYMATCH          `xml:"POLICYMATCH,omitempty"`
	POLICYPREMIUMS                 *POLICYPREMIUMS       `xml:"POLICYPREMIUMS,omitempty"`
	POLICYPAYMENTS                 *POLICYPAYMENTS       `xml:"POLICYPAYMENTS,omitempty"`
	FUNDS                          *FUNDS                `xml:"FUNDS,omitempty"`
	POLICYWITHDRAWALS              *POLICYWITHDRAWALS    `xml:"POLICYWITHDRAWALS,omitempty"`
	POLICYBANKINFO                 *POLICYBANKINFO       `xml:"POLICYBANKINFO,omitempty"`
	POLICYFUNDS                    *POLICYFUNDS          `xml:"POLICYFUNDS,omitempty"`
	POLICYFUNDSINFO                *POLICYFUNDSINFO      `xml:"POLICYFUNDSINFO,omitempty"`
	FEATURES                       *FEATURES             `xml:"FEATURES,omitempty"`
	CHARGES                        *CHARGES              `xml:"CHARGES,omitempty"`
	POLICYRIDERS                   *POLICYRIDERS         `xml:"POLICYRIDERS,omitempty"`
	DEATHBENEFIT                   *DEATHBENEFIT         `xml:"DEATH_BENEFIT,omitempty"`
	TESTVALUES                     *TESTVALUES           `xml:"TEST_VALUES,omitempty"`
	ILLUSTRATIONVALUES             *ILLUSTRATIONVALUES   `xml:"ILLUSTRATIONVALUES,omitempty"`
	POLICYTRANSACTIONS             *POLICYTRANSACTIONS   `xml:"POLICYTRANSACTIONS,omitempty"`
	TRANSACTIONSUMMARIES           *TRANSACTIONSUMMARIES `xml:"TRANSACTIONSUMMARIES,omitempty"`
	VENDORS                        *VENDORS              `xml:"VENDORS,omitempty"`
	NIGODETAILS                    *NIGODETAILS          `xml:"NIGO_DETAILS,omitempty"`
}

// POLICIES ...
type POLICIES struct {
	POLICY []*POLICY `xml:"POLICY"`
}

// DataServicesLetter ...
type DataServicesLetter struct {
	POLICIES *POLICIES `xml:"POLICIES"`
}

// WITHDRAWALARRTYPE ...
type WITHDRAWALARRTYPE *WITHDRAWALARRTYPE