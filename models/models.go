package models

type EventEnvelope struct {
	ID              string            `json:"id,omitempty"`
	CorrelationID   string            `json:"correlationid,omitempty"`
	Source          string            `json:"source,omitempty"`
	SourceLocator   string            `json:"sourcelocator,omitempty"`
	SpecVersion     string            `json:"specversion,omitempty"`
	EventType       string            `json:"type,omitempty"`
	DataContentType string            `json:"datacontenttype,omitempty"`
	DataSchema      map[string]string `json:"dataschema,omitempty"`
	Subject         string            `json:"subject,omitempty"`
	Time            string            `json:"time,omitempty"`
	//Data            interface{}       `json:"data,omitempty"`
	Data                 string        `json:"data,omitempty"`
	DataBase64           string        `json:"data_base64,omitempty"`
	Identifiers          []*Identifier `json:"identifiers,omitempty"`
	Carrier              string        `json:"carrier,omitempty"`
	ClientCode           string        `json:"clientCode,omitempty"`
	DataValues           *DataValues
	Return               string
	CaseStore            []*CaseStore
	PolicyAPIResponse    *PolicyAPIResponse
	POMApiResponse       *POMApiResponse
	RiskClassAPIResponse *RiskClassAPIResponse
}

type PolicyAPIResponse struct {
	Message string  `json:"message,omitempty"`
	Data    *Policy `json:"data,omitempty"`
}

type Identifier struct {
	IdentifierType string `json:"identifier,omitempty"`
	Value          string `json:"value,omitempty"`
}

type EDSData struct {
	Source     string `json:"source"`
	ID         string `json:"id"`
	Name       string `json:"name"`
	SequenceID *int   `json:"sequenceId"` // Pointer to handle missing sequenceId
}

type AllPolicyData struct {
	Carrier               *Carrier
	Policy                *Policy
	Transactions          []*Transaction
	PolicyPreviousVersion *Policy
	Metrics               []*PolicyMetric
	ProductRates          []*ProductRates
	Funds                 []*Fund
	Illustration          *Illustration
	IllustrationQuote     *Illustration
	NBEventData           *DataValues
	FundProduct           *FundProduct
	AnnuityProductRates   []*AnnuityProductRates
	PomAgent              *POMApiResponse
	CaseStore             []*CaseStore
	PolicyAPIResponse     *PolicyAPIResponse
}

type PolicyPagesData struct {
	Carrier               *Carrier
	Policy                *Policy
	Transactions          []*Transaction
	PolicyPreviousVersion *Policy
	Metrics               []*PolicyMetric
	ProductRates          []*ProductRates
	Funds                 []*Fund
	FundProduct           *FundProduct
	Illustration          *Illustration
	IllustrationQuote     *Illustration
	POMAgent              *POMApiResponse
}

type GeneralPolicy struct {
	Policy                *Policy
	Carrier               *Carrier
	PolicyPreviousVersion *Policy
	Funds                 []*Fund
	Illustration          *GetIllustrationResponseBody
	POMAgent              *POMApiResponse
	CaseStore             []*CaseStore
}

type Token struct {
	Token string `json:"token"`
}

type XMLDeliveryParams struct {
	CaseID          string
	ClientCode      string
	Environment     string
	OutputFilePath  string
	PolicyCount     string
	ProductCode     string
	Sender          string
	SubType         string
	TransactionName string
	XSDFilePath     string
	FileStatus      string
	Reason          string
}

// type DataValues struct {
// 	PolicyNumber                 string `json:"policyNumber,omitempty"`
// 	TransactionId                string `json:"transactionId,omitempty"`
// 	Status                       string `json:"status,omitempty"`
// 	Version                      string `json:"version,omitempty"`
// 	PreviousVersion              string `json:"previousVersion,omitempty"`
// 	PlanCode                     string `json:"planCode,omitempty"`
// 	PartyPolicyNewReferenceId    string `json:"partyPolicyNewReferenceId,omitempty"`
// 	PartyPolicyChangeReferenceId string `json:"partyPolicyChangeReferenceId,omitempty"`
// 	PartyId                      string `json:"partyId,omitempty"`
// }

type XMLFileNameParams struct {
	Environment  string
	CycleDate    string
	ClientCode   string
	LetterType   string
	PolicyNumber string
	Timestamp    string
	XMLFileName  string
	Event        string
	DeliveryType string
}

type SliceToCheck struct {
	SliceName string
	Slice     interface{}
}

type DataValues struct {
	PolicyNumber                 string           `json:"policyNumber,omitempty"`
	TransactionId                string           `json:"transactionId,omitempty"`
	Status                       string           `json:"status,omitempty"`
	Version                      string           `json:"version,omitempty"`
	PreviousVersion              string           `json:"previousVersion,omitempty"`
	PlanCode                     string           `json:"planCode,omitempty"`
	PartyPolicyNewReferenceId    string           `json:"partyPolicyNewReferenceId,omitempty"`
	PartyPolicyChangeReferenceId string           `json:"partyPolicyChangeReferenceId,omitempty"`
	PartyId                      string           `json:"partyId,omitempty"`
	EappID                       string           `json:"eappId,omitempty"`
	CaseID                       string           `json:"caseId,omitempty"`
	CorrelationID                string           `json:"correlationId,omitempty"`
	Carrier                      string           `json:"carrier,omitempty"`
	ActionType                   string           `json:"actionType,omitempty"`
	CustomerID                   string           `json:"customerId,omitempty"`
	IdpUserID                    string           `json:"idpUserId,omitempty"`
	AppStartDate                 string           `json:"appStartDate,omitempty"`
	PolicyStatus                 string           `json:"policyStatus,omitempty"`
	AppSubmissionType            string           `json:"appSubmissionType,omitempty"`
	PolicyHoldingForm            string           `json:"policyHoldingForm,omitempty"`
	PolicyHoldingStatus          string           `json:"policyHoldingStatus,omitempty"`
	ConsentInd                   string           `json:"consentInd,omitempty"`
	AppState                     string           `json:"appState,omitempty"`
	ConsentIndState              string           `json:"consentIndState,omitempty"`
	Reason                       string           `json:"reason,omitempty"`
	ConsentSubmissionType        string           `json:"consentSubmissionType,omitempty"`
	AppFormNumber                string           `json:"appFormNumber,omitempty"`
	AppVersionNumber             string           `json:"appVersionNumber,omitempty"`
	PageLastVisitedID            string           `json:"pageLastVisitedId,omitempty"`
	AppLastUpdated               string           `json:"appLastUpdated,omitempty"`
	AppExpirationDate            string           `json:"appExpirationDate,omitempty"`
	MarketingOptInd              string           `json:"marketingOptInd,omitempty"`
	AgreeToTermsInd              string           `json:"agreeToTermsInd,omitempty"`
	ReplacementIndicator         string           `json:"replacementIndicator,omitempty"`
	PageID                       string           `json:"pageId,omitempty"`
	CostBasisBefore              float64          `json:"costBasisBefore,omitempty"`
	CostBasisAfter               float64          `json:"costBasisAfter,omitempty"`
	OverrideDecisionFlag         bool             `json:"overrideDecisionFlag,omitempty"`
	OverrideResults              *OverrideResults `json:"overrideResults,omitempty"`
	Quote                        *Quote           `json:"quote,omitempty"`
	Payload                      *Payload         `json:"payload,omitempty"`
	Vendors                      []*Vendor
	SuitabilityResult            *SuitabilityResult `json:"suitabilityResult,omitempty"`
	AppointmentState             string             `json:"appointmentState,omitempty"`
	TerminationReason            string             `json:"terminationReason,omitempty"`
	OrganizationName             string             `json:"organizationName,omitempty"`
	EntityFirstName              string             `json:"entityFirstName,omitempty"`
	EntityLastName               string             `json:"entityLastName,omitempty"`
	EntityEmail                  string             `json:"entityEmail,omitempty"`
	EmailNotification            string             `json:"emailNotification,omitempty"`
	DeliveryType                 string             `json:"deliveryType,omitempty"`
	Exchange                     *Exchange          `json:"exchange,omitempty"`
	QualType                     string             `json:"qualType,omitempty"`
}

type Vendor struct {
	VendorBusinessName string
	VendorAddrLine1    string
	VendorAddrLine2    string
	VendorAddrLine3    string
	VendorCity         string
	VendorState        string
	VendorZip          string
	VendorPhnNum       string
	VendorEmailAddr    string
	VendorWebLinkUrl   string
	VendorReasonCode   string
}
type OverrideResults struct {
	DecisionStatus string `json:"decisionStatus,omitempty"`
	RiskClass      string `json:"riskClass,omitempty"`
	SourceSystem   string `json:"sourceSystem,omitempty"`
	Reason         string `json:"reason,omitempty"`
}

type Quote struct {
	DQuoteID           string            `json:"quoteId,omitempty"`
	Carrier            string            `json:"carrier,omitempty"`
	LeadID             string            `json:"leadId,omitempty"`
	RiskClass          string            `json:"riskClass,omitempty"`
	Lob                string            `json:"lob,omitempty"`
	ProductType        string            `json:"productType,omitempty"`
	Email              string            `json:"email,omitempty"`
	ProductCode        string            `json:"productCode,omitempty"`
	State              string            `json:"state,omitempty"`
	PaymentTerm        string            `json:"paymentTerm,omitempty"`
	PremiumFrequency   string            `json:"premiumFrequency,omitempty"`
	Dob                string            `json:"dob,omitempty"`
	AgeAsOfDate        string            `json:"ageAsOfDate,omitempty"`
	Gender             string            `json:"gender,omitempty"`
	Smoker             string            `json:"smoker,omitempty"`
	Health             string            `json:"health,omitempty"`
	UnderwritingMethod string            `json:"underwritingMethod,omitempty"`
	IssueAge           string            `json:"issueAge,omitempty"`
	QuoteDate          string            `json:"quoteDate,omitempty"`
	CashValue          float64           `json:"cashValue,omitempty"`
	PricingVersion     string            `json:"pricingVersion,omitempty"`
	MatchVersion       string            `json:"matchVersion,omitempty"`
	QuoteQuestions     []*QuoteQuestions `json:"quoteQuestions,omitempty"`
	ApprovedQuote      *ApprovedQuote    `json:"approvedQuote,omitempty"`
}

type QuoteQuestions struct {
	ID          string `json:"id,omitempty"`
	Code        string `json:"code,omitempty"`
	Description string `json:"description,omitempty"`
}

type ApprovedQuote struct {
	QuoteID           string  `json:"quoteId,omitempty"`
	AcctValIntRate    float64 `json:"acctValIntRate,omitempty"`
	RiskClass         string  `json:"riskClass,omitempty"`
	PaymentTerm       string  `json:"paymentTerm,omitempty"`
	IssueAge          string  `json:"issueAge,omitempty"`
	QuoteDate         string  `json:"quoteDate,omitempty"`
	CashValue         float64 `json:"cashValue,omitempty"`
	PricingVersion    string  `json:"pricingVersion,omitempty"`
	MatchVersion      string  `json:"matchVersion,omitempty"`
	FirstMonthCOI     float64 `json:"firstMonthCOI,omitempty"`
	FirstMonthSavings float64 `json:"firstMonthSavings,omitempty"`
}

type Payload struct {
	Insured *Insured `json:"insured,omitempty"`
	//ChildInsured          []*ChildInsured        `json:"childInsured,omitempty"`
	CoverageDetails *CoverageDetails `json:"coverageDetails,omitempty"`
	Underwriting    *Underwriting    `json:"underwriting,omitempty"`
	//AdditionalInformation *AdditionalInformation `json:"additionalInformation,omitempty"`
}

type Insured struct {
	FirstName  string `json:"firstName,omitempty"`
	LastName   string `json:"lastName,omitempty"`
	Name       string `json:"name,omitempty"`
	Prefix     string `json:"prefix,omitempty"`
	MiddleName string `json:"middleName,omitempty"`
	Suffix     string `json:"suffix,omitempty"`
	Gender     string `json:"gender,omitempty"`
	//Dob                        string `json:"dob,omitempty"`
	Address1                   string `json:"address1,omitempty"`
	Address2                   string `json:"address2,omitempty"`
	City                       string `json:"city,omitempty"`
	State                      string `json:"state,omitempty"`
	Zip                        string `json:"zip,omitempty"`
	Email                      string `json:"email,omitempty"`
	EmailType                  string `json:"emailType,omitempty"`
	SolicitingServiceIndicator bool   `json:"solicitingServiceIndicator,omitempty"`
}

type NBIdentification struct {
	GovernmentIDType string `json:"governmentIdType,omitempty"`
	GovernmentID     string `json:"governmentId,omitempty"`
	Jurisdiction     string `json:"jurisdiction,omitempty"`
}

type CoverageDetails struct {
	ProductMarketingName       string `json:"productMarketingName,omitempty"`
	Lob                        string `json:"lob,omitempty"`
	ProductType                string `json:"productType,omitempty"`
	ProductCode                string `json:"productCode,omitempty"`
	DeathBenefitOpt            string `json:"deathBenefitOpt,omitempty"`
	DeathBenefitComplianceTest string `json:"deathBenefitComplianceTest,omitempty"`
	PaymentFrequency           string `json:"paymentFrequency,omitempty"`
	//PlannedPremiumAmount       int                      `json:"plannedPremiumAmount,omitempty"`
	FixedCostPeriod  string `json:"fixedCostPeriod,omitempty"`
	ChildCoverageInd string `json:"childCoverageInd,omitempty"`
	//ChildCoverageAmount   int                      `json:"childCoverageAmount,omitempty"`
	Owners []*Owners `json:"owners,omitempty"`
	Agents []*Agents `json:"agents,omitempty"`
	// Primary               []*Primary               `json:"primary,omitempty"`
	// Contingent            []*Contingent            `json:"contingent,omitempty"`
	// Physician             []*Physician             `json:"physician,omitempty"`
	// Payor                 []*NBPayor               `json:"payor,omitempty"`
	// ThirdPartyDesignation []*ThirdPartyDesignation `json:"thirdPartyDesignation,omitempty"`
	// SecondaryAddressee    []*SecondaryAddressee    `json:"secondaryAddressee,omitempty"`
}

type Owners struct {
	FirstName string `json:"firstName,omitempty"`
	LastName  string `json:"lastName,omitempty"`
	Gender    string `json:"gender,omitempty"`
	//Dob                        string                `json:"dob,omitempty"`
	//Age                        int                   `json:"age,omitempty"`
	Address1 string `json:"address1,omitempty"`
	Address2 string `json:"address2,omitempty"`
	City     string `json:"city,omitempty"`
	State    string `json:"state,omitempty"`
	Zip      string `json:"zip,omitempty"`
	//Phone                      string                `json:"phone,omitempty"`
	Email                   string `json:"email,omitempty"`
	EmailType               string `json:"emailType,omitempty"`
	CommunicationPreference string `json:"communicationPreference,omitempty"`
	Name                    string `json:"name,omitempty"`
	//Prefix                     string                `json:"prefix,omitempty"`
	MiddleName string `json:"middleName,omitempty"`
	//Suffix                     string                `json:"suffix,omitempty"`
	Identification []*NBIdentification `json:"identification,omitempty"`
	//StateExecution             string                `json:"stateExecution,omitempty"`
	TrusteeName                []*TrusteeName        `json:"trusteeName,omitempty"`
	TrustBeneficiaries         []*TrustBeneficiaries `json:"trustBeneficiaries,omitempty"`
	SolicitingServiceIndicator bool                  `json:"solicitingServiceIndicator,omitempty"`
	EmailNotification          string
	DeliveryType               string
}
type Agents struct {
	FirstName  string `json:"firstName,omitempty"`
	LastName   string `json:"lastName,omitempty"`
	MiddleName string `json:"middleName,omitempty"`
	Name       string `json:"name,omitempty"`
	ExternalID string `json:"externalId,omitempty"`
	//AgentDistributorChannel    string `json:"agentDistributorChannel,omitempty"`
	//LicenseNumber              string `json:"licenseNumber,omitempty"`
	//BrokerName                 string `json:"brokerName,omitempty"`
	//BrokerExternalid           string `json:"brokerExternalid,omitempty"`
	TaxIDType                  string `json:"taxIdType,omitempty"`
	TaxID                      string `json:"taxId,omitempty"`
	SolicitingServiceIndicator bool   `json:"solicitingServiceIndicator,omitempty"`
	Classification             string `json:"classification,omitempty"`
	EmailNotification          string
	DeliveryType               string
	Address1                   string `json:"address1,omitempty"`
	Address2                   string `json:"address2,omitempty"`
	Address3                   string `json:"address3,omitempty"`
	City                       string `json:"city,omitempty"`
	State                      string `json:"state,omitempty"`
	Zip                        string `json:"zip,omitempty"`
}

type TrusteeName struct {
	FullName string `json:"fullName,omitempty"`
}

type TrustBeneficiaries struct {
	FullName string `json:"fullName,omitempty"`
}

type Underwriting struct {
	Decision              string                `json:"decision,omitempty"`
	Status                string                `json:"status,omitempty"`
	IssuedAsAppliedInd    string                `json:"issuedAsAppliedInd,omitempty"`
	UnderwritingRiskClass string                `json:"underwritingRiskClass,omitempty"`
	DecisionRiskClass     string                `json:"decisionRiskClass,omitempty"`
	UnderwritingMethod    string                `json:"underwritingMethod,omitempty"`
	TobaccoPremiumBasis   string                `json:"tobaccoPremiumBasis,omitempty"`
	TableRating           string                `json:"tableRating,omitempty"`
	UnderwritingResult    []*UnderwritingResult `json:"underwritingResult,omitempty"`
	Amendments            []*Amendments         `json:"amendments,omitempty"`
}

type Amendments struct {
	AmendmentDesc string `json:"AmendmentDesc,omitempty"`
	AmendmentCode string `json:"AmendmentCode,omitempty"`
}
type UnderwritingResult struct {
	Description string `json:"description,omitempty"`
	ReasonCode  string `json:"reasonCode,omitempty"`
}

type CaseStore struct {
	TransactionID       string          `json:"transactionId,omitempty"`
	LinkedTransactionID string          `json:"linkedTransactionId,omitempty"`
	CorrelationID       string          `json:"correlationId,omitempty"`
	TransactionType     string          `json:"transactionType,omitempty"`
	Identifiers         []Identifiers   `json:"identifiers,omitempty"`
	Status              string          `json:"status,omitempty"`
	IncomingRequest     IncomingRequest `json:"incomingRequest,omitempty"`
	Entity              *Entity         `json:"entity,omitempty"`
	EntityType          string          `json:"entityType,omitempty"`
}

type Identifiers struct {
	Identifier string `json:"identifier,omitempty"`
	Value      string `json:"value,omitempty"`
}

type IncomingRequest struct {
	AnyModeincomingRequest  string `json:"anyModeincomingRequest,omitempty"`
	AnyModeincomingRequest2 string `json:"anyModeincomingRequest2,omitempty"`
}

type Entity struct {
	Application         *Application        `json:"application,omitempty"`
	CorrelationID       string              `json:"correlationId,omitempty"`
	ApplicationID       string              `json:"applicationId,omitempty"`
	RequestID           string              `json:"requestId,omitempty"`
	ClientCode          string              `json:"clientCode,omitempty"`
	ProcessStatus       string              `json:"processStatus,omitempty"`
	SuitabilityRequest  *SuitabilityRequest `json:"suitabilityRequest,omitempty"`
	SuitabilityResult   SuitabilityResult   `json:"suitabilityResult,omitempty"`
	PartyChangeRoleType string              `json:"partyChangeRoleType,omitempty"`
	FailureDetails      []*FailureDetails   `json:"failureDetails,omitempty"`
	PartyDetails        []*PartyDetails     `json:"partyDetails,omitempty"`
}

type FailureDetails struct {
	ID                    string `json:"id,omitempty"`
	Reason                string `json:"reason,omitempty"`
	DetailedReason        string `json:"detailedReason,omitempty"`
	ProcessingReason      string `json:"processingReason,omitempty"`
	ProcessingInstruction string `json:"processingInstruction,omitempty"`
}

type PartyDetails struct {
	PartyId    string   `json:"partyId,omitempty"`
	FailureIds []string `json:"failureIds,omitempty"`
}
type Application struct {
	Roles []*Role `json:"roles,omitempty"`
}

type Role struct {
	ExternalID        string       `json:"externalId,omitempty"`
	SSN               string       `json:"ssn,omitempty"`
	RoleName          string       `json:"roleName,omitempty"`
	FirstName         string       `json:"firstName,omitempty"`
	MiddleName        string       `json:"middleName,omitempty"`
	LastName          string       `json:"lastName,omitempty"`
	Addresses         []*Addresses `json:"addresses,omitempty"`
	Email             string       `json:"email,omitempty"`
	EmailNotification string
	DeliveryType      string
}

type SuitabilityRequest struct {
	ApplicationID               string                       `json:"applicationId,omitempty"`
	RequestID                   string                       `json:"requestId,omitempty"`
	Parties                     []CaseStoreParty             `json:"parties,omitempty"`
	PolicyData                  *PolicyData                  `json:"policyData,omitempty"`
	ApplicationInfo             *ApplicationInfo             `json:"applicationInfo,omitempty"`
	FiscalData                  *FiscalData                  `json:"fiscalData,omitempty"`
	FinancialExperience         *FinancialExperience         `json:"financialExperience,omitempty"`
	FinancialObjectives         *FinancialObjectives         `json:"financialObjectives,omitempty"`
	LiquidityOptions            *LiquidityOptions            `json:"liquidityOptions,omitempty"`
	AdditionalInfo              *AdditionalInfo              `json:"additionalInfo,omitempty"`
	CaliforniaResidentQuestions *CaliforniaResidentQuestions `json:"californiaResidentQuestions,omitempty"`
}

type AdditionalInfo struct {
	CurrentResidenceOption string `json:"currentResidenceOption,omitempty"`
	ExtraFundAvailable     string `json:"extraFundAvailable,omitempty"`
}

type DisclosureAcknowledgement struct {
	ScopeAndTerms           string `json:"scopeAndTerms,omitempty"`
	TypesOfProducts         string `json:"typesOfProducts,omitempty"`
	AuthorizedInsurers      string `json:"authorizedInsurers,omitempty"`
	CompensationDescription string `json:"compensationDescription,omitempty"`
	RightToRequestInfo      string `json:"rightToRequestInfo,omitempty"`
	CompensationEstimate    string `json:"compensationEstimate,omitempty"`
	ConflictsOfInterest     string `json:"conflictsOfInterest,omitempty"`
}

type FinancialAcknowledgement struct {
	EvaluatedInformation string `json:"evaluatedInformation,omitempty"`
	InformedOfFeatures   string `json:"informedOfFeatures,omitempty"`
	CommunicatedBasis    string `json:"communicatedBasis,omitempty"`
	DisclosureRetention  string `json:"disclosureRetention,omitempty"`
}

type MaApplicationQuestionnaire struct {
	SoldPoliciesToApplicant string          `json:"soldPoliciesToApplicant,omitempty"`
	SaleInLast5Years        string          `json:"saleInLast5Years,omitempty"`
	SaleDetails             string          `json:"saleDetails,omitempty"`
	ProductDetails          []ProductDetail `json:"productDetails,omitempty"`
}

type ProductDetail struct {
	ProductType  string `json:"productType,omitempty"`
	CompanyName  string `json:"companyName,omitempty"`
	PolicyNumber string `json:"policyNumber,omitempty"`
	IssueDate    string `json:"issueDate,omitempty"`
}

type ApplicationInfo struct {
	ReplacementInd string `json:"replacementInd,omitempty"`
}

// type AdditionalInformation struct {
// 	SoldPoliciesToApplicant      string `json:"soldPoliciesToApplicant,omitempty"`
// 	PastReplacement              string `json:"pastReplacement,omitempty"`
// 	CoverageExpenseConsideration string `json:"coverageExpenseConsideration,omitempty"`
// }

type CaliforniaResidentQuestions struct {
	ApplyForMeansTestedBenefits string `json:"applyForMeansTestedBenefits,omitempty"`
}

type FinancialExperience struct {
	RateChangeAcknowledgment   string                       `json:"rateChangeAcknowledgment,omitempty"`
	TotalAnnuityValue          string                       `json:"totalAnnuityValue,omitempty"`
	FinancialProducts          *FinancialProducts           `json:"financialProducts,omitempty"`
	FinancialProductOwnerships []*FinancialProductOwnership `json:"financialProductOwnership,omitempty"`
}

type FinancialProductOwnership struct {
	ProductName string `json:"productName,omitempty"`
}

type FinancialProducts struct {
	TotalAsset []*TotalAsset `json:"totalAsset,omitempty"`
}

type TotalAsset struct {
	AssetType string `json:"assetType,omitempty"`
	Amount    string `json:"amount,omitempty"`
}
type FinancialObjectives struct {
	PurchaseReason []string `json:"purchaseReason,omitempty"`
}

type FiscalData struct {
	AnnualIncome                string   `json:"annualIncome,omitempty"`
	NetWorth                    string   `json:"netWorth,omitempty"`
	AnnualExpenses              string   `json:"annualExpenses,omitempty"`
	SourceOfFunds               []string `json:"sourceOfFunds,omitempty"`
	FederalTaxBand              string   `json:"federalTaxBand,omitempty"`
	SurrPeriodMaterialChangeExp string   `json:"surrPeriodMaterialChangeExp,omitempty"`
}

type InvestmentRiskInfo struct {
	RiskTolerance            string `json:"riskTolerance,omitempty"`
	RiskToleranceDesc        string `json:"riskToleranceDesc,omitempty"`
	AnnuityFitsRiskTolerance string `json:"annuityFitsRiskTolerance,omitempty"`
}

type LiquidityOptions struct {
	FutureDistributionMethods []FutureDistributionMethods `json:"futureDistributionMethods,omitempty"`
	FirstPayoutOption         string                      `json:"firstPayoutOption,omitempty"`
}

type FutureDistributionMethods struct {
	DistributionMethods string `json:"distributionMethods,omitempty"`
	OptedYears          string `json:"optedYears,omitempty"`
}

type CaseStoreParty struct {
	PartyType         string          `json:"partyType,omitempty"`
	RoleName          string          `json:"roleName,omitempty"`
	RoleType          string          `json:"roleType,omitempty"`
	FullName          string          `json:"fullName,omitempty"`
	Person            CaseStorePerson `json:"person,omitempty"`
	GovtIDInfo        []GovtIDInfo    `json:"govtIdInfo,omitempty"`
	Addresses         []Addresses     `json:"addresses,omitempty"`
	EmailNotification string
	DeliveryType      string
}

type Addresses struct {
	AddressType  string   `json:"addressType,omitempty"`
	Line1        string   `json:"line1,omitempty"`
	Line2        string   `json:"line2,omitempty"`
	Line3        string   `json:"line3,omitempty"`
	City         string   `json:"city,omitempty"`
	State        string   `json:"state,omitempty"`
	Country      string   `json:"country,omitempty"`
	Zip          string   `json:"zip,omitempty"`
	AddressLines []string `json:"addressLines,omitempty"`
}

type GovtIDInfo struct {
	GovtID        string `json:"govtId,omitempty"`
	GovtIDType    string `json:"govtIdType,omitempty"`
	GovtIDExpDate string `json:"govtIdExpDate,omitempty"`
}

type CaseStorePerson struct {
	FirstName              string `json:"firstName,omitempty"`
	MiddleName             string `json:"middleName,omitempty"`
	LastName               string `json:"lastName,omitempty"`
	Gender                 string `json:"gender,omitempty"`
	Age                    string `json:"age,omitempty"`
	MartialStatus          string `json:"martialStatus,omitempty"`
	BirthCountry           string `json:"birthCountry,omitempty"`
	BirthJurisdictionState string `json:"birthJurisdictionState,omitempty"`
}

type PolicyData struct {
	LineOfBusiness   string `json:"lineOfBusiness,omitempty"`
	ProductCode      string `json:"productCode,omitempty"`
	ProductType      string `json:"productType,omitempty"`
	QualPlanType     string `json:"qualPlanType,omitempty"`
	AnnualPaymentAmt string `json:"annualPaymentAmt,omitempty"`
}

type SuitabilityResult struct {
	ApplicationID           string                  `json:"applicationId,omitempty"`
	RequestID               string                  `json:"requestId,omitempty"`
	ClientCode              string                  `json:"clientCode,omitempty"`
	CaseID                  string                  `json:"caseID,omitempty"`
	Status                  string                  `json:"status,omitempty"`
	NigoDetails             NigoDetails             `json:"nigoDetails,omitempty"`
	SuitabilityRulesResult  SuitabilityRulesResult  `json:"suitabilityRulesResult,omitempty"`
	SuitabilityReviewResult SuitabilityReviewResult `json:"suitabilityReviewResult,omitempty"`
}

type NigoDetails struct {
	Nigos []Nigo `json:"nigos,omitempty"`
}

type Nigo struct {
	ID                    string `json:"id,omitempty"`
	Nmid                  string `json:"nmid,omitempty"`
	Type                  string `json:"type,omitempty"`
	Category              string `json:"category,omitempty"`
	Reason                string `json:"reason,omitempty"`
	DetailedReason        string `json:"detailedReason,omitempty"`
	RuleID                string `json:"ruleId,omitempty"`
	Rule                  string `json:"rule,omitempty"`
	ProcessingInstruction string `json:"processingInstruction,omitempty"`
	Resolved              bool   `json:"resolved,omitempty"`
	QueueName             string `json:"queueName,omitempty"`
	IsTerminal            bool   `json:"isTerminal,omitempty"`
}

type SuitabilityReviewResult struct {
	ReviewTaskID  string       `json:"reviewTaskID,omitempty"`
	ReviewRules   []ReviewRule `json:"reviewRules,omitempty"`
	ReviewStatus  string       `json:"reviewStatus,omitempty"`
	DeclineReason []string     `json:"declineReason,omitempty"`
}

type ReviewRule struct {
	Issue            string `json:"issue,omitempty"`
	Rule             string `json:"rule,omitempty"`
	ApplicationValue string `json:"applicationValue,omitempty"`
}

type SuitabilityRulesResult struct {
	RulesResult string   `json:"rulesResult,omitempty"`
	RulesList   []string `json:"rulesList,omitempty"`
	NigoList    []string `json:"nigoList,omitempty"`
}

type Exchange struct {
	TransactionCompAccountNumber string  `json:"transactionCompAccountNumber,omitempty"`
	CompAccountNumber            string  `json:"compAccountNumber,omitempty"`
	CompName                     string  `json:"compName,omitempty"`
	CompAddressLine1             string  `json:"compAddressLine1,omitempty"`
	CompAddressLine2             string  `json:"compAddressLine2,omitempty"`
	CompAddressLine3             string  `json:"compAddressLine3,omitempty"`
	CompCity                     string  `json:"compCity,omitempty"`
	CompState                    string  `json:"compState,omitempty"`
	CompZip                      string  `json:"compZip,omitempty"`
	DeliveryMode                 string  `json:"deliveryMode,omitempty"`
	ErNumber                     string  `json:"erNumber,omitempty"`
	TotalAmount                  float64 `json:"totalAmount,omitempty"`
	FaxNumber                    string  `json:"faxNumber,omitempty"`
}

type POMApiResponse struct {
	Count   int      `json:"count"`
	Results []Result `json:"results"`
}

type Result struct {
	ProducerType           string          `json:"producerType"`
	PartyID                string          `json:"partyId"`
	FirstName              string          `json:"firstName"`
	LastName               string          `json:"lastName"`
	MiddleName             string          `json:"middleName"`
	NationalProducerNumber string          `json:"nationalProducerNumber"`
	Email                  string          `json:"email"`
	BusinessPhone          BusinessPhone   `json:"businessPhone"`
	BusinessAddress        BusinessAddress `json:"businessAddress"`
}

type BusinessPhone struct {
	CountryCode string `json:"countryCode"`
	Number      string `json:"number"`
	Extension   string `json:"extension"`
	Type        string `json:"type"`
}

type BusinessAddress struct {
	Type    string `json:"type"`
	Line    string `json:"line"`
	Line2   string `json:"line2"`
	City    string `json:"city"`
	State   string `json:"state"`
	Country string `json:"country"`
	ZipCode string `json:"zipCode"`
}

// Translation mapping structures
type RiskClassAPIResponse struct {
	Message string           `json:"message"`
	Data    *TranslationData `json:"data"`
	Status  int              `json:"status"`
}

type TranslationData struct {
	CarrierID  string      `json:"carrierId"`
	PlanCode   string      `json:"planCode"`
	Categories []*Category `json:"categories"`
}

type Category struct {
	CategoryName string         `json:"categoryName"`
	Translations []*Translation `json:"translations"`
}

type Translation struct {
	SystemOfRecordValue string                 `json:"systemOfRecordValue"`
	CarrierValue        string                 `json:"carrierValue"`
	Metadata            map[string]interface{} `json:"metadata,omitempty"`
}
