package main

import (
	"fmt"
	"correspondence-composer/gateways/productapi"
	"correspondence-composer/mapper/helpers"
	"correspondence-composer/models"
	xmlgenmodels "correspondence-composer/models/generated"
)

func main() {
	// Test the surrender charges functionality
	fmt.Println("Testing surrender charges...")
	
	// Create mock config
	config := productapi.Config{
		ProductBenefitspMTXGCSVRT_MYGASampleFile:    "./gateways/productapi/mock_data/sample_productbenefit_MTXGCSVRT_MYGA.json",
		ProductBenefitspMTXFWAPCT_MYGASampleFile:    "./gateways/productapi/mock_data/sample_productbenefit_MTXFWAPCT_MYGA.json",
		ProductBenefitspMTXRENEWALSC_MYGASampleFile: "./gateways/productapi/mock_data/sample_productbenefit_MTXRENEWALSC_MYGA.json",
		AnnuityProductIdentifier:                    "annuityProductApi",
		AnnuityProductContractYearIdentifier:        "annuityProductApiContractYear",
		AnnuityProductGuaranteedYearIdentifier:      "annuityProductApiGuaranteedYear",
	}
	
	// Load mock data
	mockData, err := productapi.GetMockData(config)
	if err != nil {
		fmt.Printf("Error loading mock data: %v\n", err)
		return
	}
	
	// Create test policy data
	policy := &models.Policy{
		DistributionDetails: models.DistributionDetails{
			PolicyFunds: []models.PolicyFund{
				{
					InterestGuaranteedPeriod: 7, // Test with 7 years
				},
			},
		},
	}
	
	// Create test mapping
	productRateMappings := []models.ProductRateXMLSurrenderChrages{
		{
			RateID:               "MTXRENEWALSC_MYGA",
			APIEndpoint:          "GuaranteedYear",
			SurrenderChargeType:  "SUBSEQUENT",
			FilterByYear:         false,
		},
	}
	
	// Call the function
	result := helpers.MapLCPolicySurrenderChargesToPolicy(
		policy,
		mockData.MockProductBenefitRatesDataAnnuity.Data,
		productRateMappings,
	)
	
	if result != nil && len(result.SURRENDERCHARGE) > 0 {
		fmt.Printf("SUCCESS: Generated %d surrender charges\n", len(result.SURRENDERCHARGE))
		for i, charge := range result.SURRENDERCHARGE {
			fmt.Printf("Charge %d: Year=%d, Percentage=%s, Type=%s\n", 
				i+1, charge.SURRCHRGTERMYEAR, charge.SURRCHRGTERMPERC, charge.SURRCHRGTYPE)
		}
	} else {
		fmt.Println("FAILED: No surrender charges generated")
	}
}
