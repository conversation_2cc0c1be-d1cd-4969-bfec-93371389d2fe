package utils

import (
	"context"
	"correspondence-composer/models"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"reflect"
	"strconv"
	"strings"
)

var (
	ctx = context.Background()
)

func GetProductRateParametersFromPolicy(policy *models.Policy) *models.PolicyParameterValues {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	var input models.PolicyParameterValues

	if policy.Coverage != nil {
		if policy.Coverage.CoverageLayers[0] != nil {
			input.IssueAge = policy.Coverage.CoverageLayers[0].CoverageParticipants[0].IssueAge
			input.RiskClass = policy.Coverage.CoverageLayers[0].CoverageParticipants[0].RiskClass
			input.CvgPartyId = policy.Coverage.CoverageLayers[0].CoverageParticipants[0].PartyID
			input.ContractYear = policy.PolicyYear
		} else {
			input.IssueAge = new(int)
			input.RiskClass = "N/A"
			input.CvgPartyId = "N/A"
		}
		input.FixedCostPeriod = strconv.Itoa(policy.FixedCostPeriod)
		input.FixedCostPeriodPlus1 = strconv.Itoa(policy.FixedCostPeriod + 1)
		input.TotalCoverageAmt = policy.Coverage.TotalCoverageAmt
		input.IssueDate = policy.PolicyDates.IssueDate

		for _, party := range policy.Parties {
			if party.PartyID == input.CvgPartyId {
				input.Gender = party.Gender
			}
		}

		return &input
	}
	return nil
}

func GetProductRateInputs(carrierId string, indentifiers []*models.Identifier, issueDate string) []*models.ProductBenefitRateInput {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	var prdctBeneRateInputs []*models.ProductBenefitRateInput

	for _, identifier := range indentifiers {
		var input models.ProductBenefitRateInput

		if identifier != nil && (identifier.IdentifierType == "productApi" || identifier.IdentifierType == "annuityProductApi" || identifier.IdentifierType == "annuityProductApiContractYear" || identifier.IdentifierType == "annuityProductApiGuaranteedYear") {
			tagValuePairs := strings.Split(identifier.Value, "^")

			input.CarrierId = carrierId
			input.IssueDate = issueDate
			input.IssueType = identifier.IdentifierType

			for _, tagValuePair := range tagValuePairs {
				tagValues := strings.Split(tagValuePair, ":")

				if tagValues[0] == "ProductID" {
					input.ProductId = tagValues[1]
				}

				if tagValues[0] == "BenefitId" {
					input.BenefitId = tagValues[1]
				}

				if tagValues[0] == "ConfiguredSettingId" {
					input.ConfiguredSettingId = tagValues[1]
				}
			}
			prdctBeneRateInputs = append(prdctBeneRateInputs, &input)
		}

	}
	return prdctBeneRateInputs
}

func GetProductRateVersionInputs(carrierId string,
	indentifiers []*models.Identifier,
	productRateParametersFromPolicy *models.PolicyParameterValues,
	planCode string) []*models.ProductBenefitRateVersionInput {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	var versionCodeinputs []*models.ProductBenefitRateVersionInput

	for _, identifier := range indentifiers {
		var inputs models.ProductBenefitRateVersionInput

		if identifier != nil && (identifier.IdentifierType == "productApiVersion" || identifier.IdentifierType == "productApiVersionFxdCost" || identifier.IdentifierType == "productApiVersionFxdCostPlus1") {

			tagValuePairs := strings.Split(identifier.Value, "^")

			for _, tagValuePair := range tagValuePairs {

				inputs.CarrierId = carrierId

				tagValues := strings.Split(tagValuePair, ":")
				if tagValues[0] == "ProductID" {
					inputs.ProductId = tagValues[1]
				}
				if tagValues[0] == "BenefitId" {
					inputs.BenefitId = tagValues[1]
				}

				if tagValues[0] == "ConfiguredSettingId" && identifier.IdentifierType == "productApiVersionFxdCostPlus1" {
					inputs.ConfiguredSettingId = tagValues[1] + productRateParametersFromPolicy.FixedCostPeriod
				}

				if tagValues[0] == "ConfiguredSettingId" && identifier.IdentifierType == "productApiVersionFxdCost" {
					inputs.ConfiguredSettingId = tagValues[1]
					if productRateParametersFromPolicy.FixedCostPeriod != "0" && productRateParametersFromPolicy.FixedCostPeriod != "null" {
						inputs.ConfiguredSettingId = inputs.ConfiguredSettingId + productRateParametersFromPolicy.FixedCostPeriod
					}
				} else if tagValues[0] == "ConfiguredSettingId" {
					inputs.ConfiguredSettingId = tagValues[1]
				}
				if productRateParametersFromPolicy.IssueDate != "" {
					inputs.VersionCode = productRateParametersFromPolicy.IssueDate
				}
				if tagValues[0] == "RiskClass" {
					inputs.RiskClass = productRateParametersFromPolicy.RiskClass
				}
				if tagValues[0] == "IssueAge" {
					inputs.IssueAge = *productRateParametersFromPolicy.IssueAge
				}
				if tagValues[0] == "Gender" {
					inputs.Gender = productRateParametersFromPolicy.Gender
				}
				if tagValues[0] == "CoverageAmount" {
					inputs.TotalCoverageAmt = productRateParametersFromPolicy.TotalCoverageAmt
				}
				if tagValues[0] == "ContractYear" {
					inputs.ContractYear = productRateParametersFromPolicy.ContractYear
				}
			}
			versionCodeinputs = append(versionCodeinputs, &inputs)

		}

	}

	return versionCodeinputs
}

func GetZaharaAnnuityProductRateInputs(carrierId string, indentifiers []*models.Identifier, issueDate string, productRateParametersFromPolicy *models.PolicyParameterValues) []*models.ProductBenefitRateInput {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	var prdctBeneRateInputs []*models.ProductBenefitRateInput

	for _, identifier := range indentifiers {
		var input models.ProductBenefitRateInput

		if identifier != nil && (identifier.IdentifierType == "annuityProductRateAPI") {
			tagValuePairs := strings.Split(identifier.Value, "^")

			input.CarrierId = carrierId
			input.IssueDate = issueDate
			input.IssueType = identifier.IdentifierType

			for _, tagValuePair := range tagValuePairs {
				tagValues := strings.Split(tagValuePair, ":")

				if tagValues[0] == "ProductID" {
					input.ProductId = tagValues[1]
				}

				if tagValues[0] == "BenefitId" {
					input.BenefitId = tagValues[1]
				}

				if tagValues[0] == "ConfiguredSettingId" {
					input.ConfiguredSettingId = tagValues[1]
				}
				if tagValues[0] == "IssueAge" {
					input.IssueAge = *productRateParametersFromPolicy.IssueAge
				}
				if tagValues[0] == "Gender" {
					input.Gender = productRateParametersFromPolicy.Gender
				}
				if tagValues[0] == "ContractYear" {
					input.ContractYear = productRateParametersFromPolicy.ContractYear
				}
			}
			prdctBeneRateInputs = append(prdctBeneRateInputs, &input)
		}

	}
	return prdctBeneRateInputs
}

func SetProductRatesDatapoint(productRates []*models.ProductRates, productRateMappings []models.ProductRateXMLMappings, policyValues interface{}) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	var xmlModelValue reflect.Value
	var convertedValue = reflect.ValueOf(nil)
	var condtionalfieldval = reflect.ValueOf(nil)
	xmlModelValue = reflect.ValueOf(policyValues)

	for _, xmlMapping := range productRateMappings {
		for _, rates := range productRates {

			if rates != nil {

				if rates.RateId+rates.Benefit == xmlMapping.Parameter+xmlMapping.BenID {

					xmlField := strings.ReplaceAll(xmlMapping.XMLTag, "_", "")
					field := xmlModelValue.Elem().FieldByName(xmlField)

					if xmlMapping.NodeFieldName != "" {

						conditionalField := xmlModelValue.Elem().FieldByName(strings.ReplaceAll(xmlMapping.NodeFieldName, "_", ""))

						condtionalfieldval = reflect.ValueOf(conditionalField)

						val := fmt.Sprintf("%v", condtionalfieldval)

						if val == xmlMapping.Value {

							if field != (reflect.Value{}) {
								if len(rates.Rates.Ages.Age) == 0 {
									rates.Rates.Ages.Age[0] = 0
								}
								age := fmt.Sprintf("%v", rates.Rates.Ages.Age[0])
								if len(rates.Rates.Ages.Age) == 1 {
									convertedValue = reflect.ValueOf(age)
									field.Set(convertedValue)
								}
							}

						}

					} else {

						if field != (reflect.Value{}) {
							if len(rates.Rates.Ages.Age) == 0 {
								rates.Rates.Ages.Age[0] = 0
							}
							age := fmt.Sprintf("%v", rates.Rates.Ages.Age[0])
							convertedValue = reflect.ValueOf(age)
							field.Set(convertedValue)
						}

					}
				}
			}
		}
	}

}

// For ZH
func GetProductRatesDataSetForLifePolicies(rateReturnInterface map[string]interface{}, productRatesReturn *models.ProductRates, issueAge int) (*models.ProductRates, error) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	//Datapoint Names
	effDate := "effectiveDate"
	parent := "ages"
	fieldName := "All"
	var ageData interface{}

	if rateReturnInterface[effDate] != nil {
		datefield := fmt.Sprintf("%s", rateReturnInterface[effDate])
		if len(datefield) < 18 {
			return nil, errors.New("GetProductRatesDataSetForLifePolicies.Data effective date value format is incorrect: " + datefield)
		}

		rateStartDate := datefield[4:14]

		data := rateReturnInterface[effDate].(map[string]interface{})[rateStartDate]
		if data == nil {
			return nil, errors.New("GetProductRatesDataSetForLifePolicies.Data could not set the variable data")
		}

		dateArray := data.([]interface{})
		if dateArray == nil {
			return nil, errors.New("GetProductRatesDataSetForLifePolicies.dateArray could not set the variable dateArray")
		}

		mdata := dateArray[0].(map[string]interface{})[parent]
		if mdata == nil {
			return nil, errors.New("GetProductRatesDataSetForLifePolicies.mdata could not set the variable mdata")
		}

		ageData = mdata.(map[string]interface{})[fieldName]

		if ageData == nil {
			fieldName = strconv.Itoa(issueAge) //string(issueAge)
			ageData = mdata.(map[string]interface{})[fieldName]
		}

		if ageData == nil {
			return nil, errors.New("GetProductRatesDataSetForLifePolicies.ageData age value is missing from product rate api")
		}

		age := ageData.([]interface{})
		
		// TODO: This will be uncommented post discussion with product team for Farmers
		// if len(age) > 3 {
		// 	age = age[:3]
		// }

		productRatesReturn.Rates = &models.Rates{
			Gender:                 dateArray[0].(map[string]interface{})["gender"],
			RiskClass:              dateArray[0].(map[string]interface{})["riskClass"],
			SmokeClass:             dateArray[0].(map[string]interface{})["smokeClass"],
			CoverageBandLowerBound: dateArray[0].(map[string]interface{})["coverageBandLowerBound"],
			Ages: &models.Age{
				Age: age,
			},
		}

		if dateArray[0].(map[string]interface{})["configuredItem"] != nil {
			productRatesReturn.RateId = dateArray[0].(map[string]interface{})["configuredItem"].(string)
		}
		return productRatesReturn, nil
	}
	return nil, errors.New("GetProductRatesDataSetForLifePolicies Effective Date is missing from Product Rate API")
}

func GetProductRatesDataSetForAnnuities(jsonResponse []byte, productRatesReturn *models.AnnuityProductRates, eventIdentifier string) (*models.AnnuityProductRates, error) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	var annuityProductRates models.AnnuityProductRates
	annuityProductRates.Carrier = productRatesReturn.Carrier
	annuityProductRates.Benefit = productRatesReturn.Benefit
	annuityProductRates.Product = productRatesReturn.Product
	annuityProductRates.RateId = productRatesReturn.RateId

	fmt.Printf("DEBUG: GetProductRatesDataSetForAnnuities - eventIdentifier: %s, RateId: %s\n", eventIdentifier, annuityProductRates.RateId)
	fmt.Printf("DEBUG: JSON Response: %s\n", string(jsonResponse))

	var target interface{}

	switch eventIdentifier {
	case "annuityProductApi":
		target = &annuityProductRates.EffectiveDate
		fmt.Printf("DEBUG: Target set to EffectiveDate\n")
	case "annuityProductApiContractYear":
		target = &annuityProductRates.ContractYear
		fmt.Printf("DEBUG: Target set to ContractYear\n")
	case "annuityProductApiGuaranteedYear":
		target = &annuityProductRates.GuaranteedYear
		fmt.Printf("DEBUG: Target set to GuaranteedYear\n")
	default:
		log.Println("Warning: Unrecognized event identifier:", eventIdentifier)
		return &annuityProductRates, nil
	}

	if err := unmarshalAndPrint(jsonResponse, target, "error parsing response"); err != nil {
		fmt.Printf("DEBUG: Error unmarshaling JSON: %v\n", err)
		return nil, errors.New("GetProductRatesDataSetForAnnuities. could not set the variable data")
	}

	// Debug: Print the result after unmarshaling
	if eventIdentifier == "annuityProductApiGuaranteedYear" {
		fmt.Printf("DEBUG: After unmarshaling - GuaranteedYear: %+v\n", annuityProductRates.GuaranteedYear)
		fmt.Printf("DEBUG: GuaranteedYear.GuaranteedYear: %+v\n", annuityProductRates.GuaranteedYear.GuaranteedYear)
	}

	return &annuityProductRates, nil
}

func unmarshalAndPrint(jsonResponse []byte, target interface{}, errorMessage string) error {
	if err := json.Unmarshal(jsonResponse, target); err != nil {
		return fmt.Errorf("%s: %w", errorMessage, err)
	}

	return nil
}

// for LC
func SetProductRatesDatapointForAnnuity(productRates []*models.AnnuityProductRates, productRateMappings []models.ProductRateXMLMappings, policyValues interface{}) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	var xmlModelValue reflect.Value
	var convertedValue = reflect.ValueOf(nil)
	xmlModelValue = reflect.ValueOf(policyValues)

	for _, xmlMapping := range productRateMappings {
		for _, rates := range productRates {
			if rates != nil {
				if rates.RateId+rates.Benefit == xmlMapping.Parameter+xmlMapping.BenID {
					xmlField := strings.ReplaceAll(xmlMapping.XMLTag, "_", "")
					field := xmlModelValue.Elem().FieldByName(xmlField)
					if rates.EffectiveDate.EffectiveDate != nil {
						for _, value := range rates.EffectiveDate.EffectiveDate {

							rateValue := fmt.Sprintf("%v", value)
							convertedValue = reflect.ValueOf(rateValue)
							if field.IsValid() && field.CanSet() {
								field.Set(convertedValue)
							}
						}

					}

				}
			}
		}
	}

}
func SetNestedProductRatesDatapointForAnnuity(productRates []*models.AnnuityProductRates, productRateMappings []models.ProductRateXMLSurrenderChrages) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer PanicHandler(ctx)

	var withdrawalFreePct string

	for _, rate := range productRates {
		for _, mapping := range productRateMappings {
			if len(mapping.Occurance) > 0 && rate.RateId == mapping.RateID {
				contractYearData := rate.ContractYear.ContractYear

				if details, ok := contractYearData[mapping.Occurance]; ok && len(details) > 0 {
					effectiveDate := details[0].EffectiveDate
					if effectiveDate != nil {
						for _, value := range effectiveDate {
							withdrawalFreePct = value
							return withdrawalFreePct // Return on the first found value
						}
					}
				}
			}
		}
	}

	return withdrawalFreePct // Returns empty string if no value found
}
