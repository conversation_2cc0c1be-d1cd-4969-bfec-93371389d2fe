<?xml version="1.0" encoding="utf-8"?>
<xsd:schema attributeFormDefault="unqualified" elementFormDefault="qualified"
	xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<xsd:simpleType name="AlphanumericString">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[a-zA-Z0-9_ \-.:]*"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="PhoneNumber">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[a-zA-Z0-9_ \-().]*"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="Email">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[^@]+@[^\.]+\..+"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="OptionalDate">
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="\d{4}-\d{2}-\d{2}|"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:element name="DataServicesLetter">
		<xsd:complexType>
			<xsd:sequence>
				<xsd:element name="POLICIES">
					<xsd:complexType>
						<xsd:sequence>
							<xsd:element name="POLICY" minOccurs="1" maxOccurs="unbounded">
								<xsd:complexType>
									<xsd:sequence>
										<xsd:element name="REC_TYPE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_ROLE_TYPE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_DISCOUNT_INDICATOR" type="xsd:string" minOccurs="0"/>

										<xsd:element name="POL_FULL_NAME" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_TRACKING_ID" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_EXTERNAL_TRACKING_ID" type="xsd:string"  minOccurs="0" />
										<xsd:element name="POL_SYS_CODE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_SOURCE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_SERVICE_NAME" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_SERVICE_DESC" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_DOC_TYPE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_CONT" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_POL_NUM" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_CASE_ID" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PROD_CODE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_LOB" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_QUAL_TYPE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_QUAL_DESC" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PLAN_CODE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PLAN_TYPE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PRODUCT" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_TXN_DESC" type="xsd:string"  minOccurs="0" />
										<xsd:element name="POL_TXN_AMT" type="xsd:decimal"  minOccurs="0" />
										<xsd:element name="POL_PROD_SHORT_NAME" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PRDCT_MKTG_NAME" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_MKTG_NAME" type="xsd:string"  minOccurs="0" />
										<xsd:element name="POL_PRODUCT_TYPE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_COMP_NAME" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PRDCT_COMP" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PRCSG_COMP" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_ISSUE_STATE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_RESIDENCE_STATE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_ISSUE_TYPE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_TERM" type="xsd:nonNegativeInteger"  minOccurs="0"/>
										<xsd:element name="POL_STATUS" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PREM_AMT" type="xsd:decimal" minOccurs="0" />
										<xsd:element name="POL_PREM_MODE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_PAYMENT_FREQUENCY" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_ISSUE_AGE" type="xsd:nonNegativeInteger" minOccurs="0"/>
										<xsd:element name="POL_CONT_AGE" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_RISK_CLASS" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_EFFECTIVE_DATE" type="OptionalDate" minOccurs="0"/>
										<xsd:element name="POL_ISSUE_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_START_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_MATURITY_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_TERM_DATE" type="OptionalDate" minOccurs="0"/>
										<xsd:element name="POL_AS_OF_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_CURR_ANNIV_BEGIN_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_CURR_ANNIV_END_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_LAST_ANNIV_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_CURR_ANNIV_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_NEXT_ANNIV_DATE" type="OptionalDate" minOccurs="0"/>
										<xsd:element name="POL_NEXT_ANNIV_BEGIN_DATE" type="OptionalDate" minOccurs="0"/>
										<xsd:element name="POL_NEXT_ANNIV_END_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_FACE_VALUE" type="xsd:nonNegativeInteger"  minOccurs="0"/>
										<xsd:element name="POL_CYCLE_DATE" type="OptionalDate"  minOccurs="0"/>
										<xsd:element name="POL_CURR_YR" type="xsd:nonNegativeInteger"  minOccurs="0"/>
										<xsd:element name="POL_BONUS_START_DATE" type="xsd:unsignedByte"  minOccurs="0"/>
										<xsd:element name="POL_GUARANTEED_INTEREST_RATE_YEAR" type="xsd:unsignedByte"  minOccurs="0"/>
										<xsd:element name="POL_OWNER_SAME_AS_INS" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_INFORCE_ASV_IND" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_COST_BASIS" type="xsd:decimal"  minOccurs="0"/>
										<xsd:element name="POL_IS_MEC" type="xsd:boolean"  minOccurs="0"/>
										<xsd:element name="POL_SURRENDER_DATE" type="OptionalDate" minOccurs="0"/>
										<xsd:element name="POL_FIXED_COST_END_PERIOD" type="xsd:unsignedByte"   minOccurs="0"/>
										<xsd:element name="POL_FIXED_COST_POST_START_PERIOD" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_MONTHLY_DEDUCTION_DATE" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_ACCOUNT_VALUE" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_WITHHOLDING_AMOUNT" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_AP_IMAGE_PATH" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_AP_IMAGE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_SIGNED_APP" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_SIGNED_OVERFLOW_APP" type="xsd:string"  minOccurs="0" />
										<xsd:element name="POL_SIGNED_SUPP_APP" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_SIGNED_AMENDMENT_FORM" type="xsd:string"  minOccurs="0" />
										<xsd:element name="POL_SIGNED_EFT_FORM" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_SIGNED_ABR_DISCLOSURE" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_REPLACMENT_FORM" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_HIPAA_FORM" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_CONSENT_FORM" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_SIGNED_ILLUSTRATION" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_SURRENDER_PERIOD_OPT" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_MIB_POST_NOTICE_DISC" type="xsd:string"  minOccurs="0"/>
										<xsd:element name="POL_ANN_START_DATE" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_TAX_YEAR" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_TAX_DOCUMENTS" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_APPLICATION_ID" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_TERMINATION_REASON_CODE" type="xsd:string" minOccurs="0" />
                    <xsd:element name="POL_ENDOWMENT_DATE" type="xsd:string" minOccurs="0" />
                    <xsd:element name="POL_ENDOWMENT_AMOUNT" type="xsd:string" minOccurs="0" />
                    <xsd:element name="POL_ENDOWMENT_FACTOR" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_INITIAL_PREMIUM_APPLIED_AMOUNT" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="POL_ANNUAL_TARGET_PREMIUM" type="xsd:string" minOccurs="0"/>
                    <xsd:element name="POL_FIXED_COST_DELIVERY_END_PERIOD" type="xsd:string" minOccurs="0"/>
					                    <xsd:element name="POL_TARGET_ROLE" type="xsd:string" minOccurs="0"/>
										<xsd:element name="POL_AMENDMENTS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POL_AMENDMENT" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="POL_AMENDMENT_CODE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="POL_AMENDMENT_DESC" type="xsd:string" minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POL_COST_BASIS_AMOUNT_BEFORE" type="xsd:string" minOccurs="0" />
										<xsd:element name="POL_COST_BASIS_AMOUNT_AFTER" type="xsd:string" minOccurs="0" />
										<xsd:element name="DOCUMENTS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="DOCUMENT" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="DOCUMENT_NAME_1" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_2" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_3" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_4" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_5" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_6" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_7" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_8" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_9" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DOCUMENT_NAME_10" type="xsd:string" minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="FUND_DETAILS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="FUND_DETAIL" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="FUND_NAME_1" type="xsd:string"/>
																<xsd:element name="FUND_ALLOCATION_PERCENTAGE_1" type="xsd:unsignedByte"/>
																<xsd:element name="FUND_NAME_2" type="xsd:string"/>
																<xsd:element name="FUND_ALLOCATION_PERCENTAGE_2" type="xsd:unsignedByte"/>
																<xsd:element name="FUND_NAME_3" type="xsd:string"/>
																<xsd:element name="FUND_ALLOCATION_PERCENTAGE_3" type="xsd:unsignedByte"/>
																<xsd:element name="FUND_NAME_4" type="xsd:string"/>
																<xsd:element name="FUND_ALLOCATION_PERCENTAGE_4" type="xsd:unsignedByte"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="PARTIES" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="PARTY" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="PARTY_CONT" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="PARTY_ID" type="AlphanumericString"/>
																<xsd:element name="PARTY_PCT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="PARTY_TAX_ID" type="xsd:string" minOccurs="0"/>
																<xsd:element name="CHILD_FULL_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="CHILD_DOB" type="xsd:string" minOccurs="0"/>
																<xsd:element name="PARTY_EXTERNAL_ID" type="xsd:string" minOccurs="0"/>
																<xsd:element name="PARTY_APPROVAL_IND" type="xsd:string" minOccurs="0"/>
																<xsd:element name="DELIVERY" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="RELATED_ROLE_ID" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="ROLE_OPT_ID" type="xsd:int"  minOccurs="0"/>
																			<xsd:element name="PARTY_DELIVERY_IND" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="PARTY_DELIVERY_TYPE" type="xsd:string"  minOccurs="0" />
																			<xsd:element name="PARTY_DELIVERY_EMAIL_NOTIFICATION" type="xsd:string"  minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="ROLES" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element maxOccurs="unbounded" name="ROLE" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="ROLE_ID" type="xsd:string"  />
																						<xsd:element name="ROLE_CODE" type="AlphanumericString"  />
																						<xsd:element name="ROLE_TYPE" type="AlphanumericString"  />
																						<xsd:element name="ROLE_NAME" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ROLE_STATUS" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="RELATION_TO_INS" type="xsd:string"  minOccurs="0" />
																						<xsd:element name="ROLE_CC_IND" type="xsd:string"  minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="PARTY_TYPE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="PARTY_FULL_NAME" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PARTY_PREFIX" type="xsd:string"  minOccurs="0" />
																<xsd:element name="PARTY_FST_NAME" type="xsd:string"  minOccurs="0" />
																<xsd:element name="PARTY_MI" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PARTY_LST_NAME" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PARTY_SUFFIX" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="ADDRESSES">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element maxOccurs="unbounded" name="ADDRESS" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="ADDR_TYPE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ADDR_LINE1" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ADDR_LINE2" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ADDR_LINE3" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ADDR_CITY" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="ADDR_STATE" type="xsd:string"  minOccurs="0" />
																						<xsd:element name="ADDR_ZIP" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ADDR_ZIP_EXTN" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ADDR_CNTRY_CODE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ADDR_STATUS" type="xsd:string"  minOccurs="0" />
																						<xsd:element name="ADDR_PREF_IND" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="ADDR_CLASSIFICATION" type="xsd:string" minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="PARTY_DOB" type="OptionalDate"  minOccurs="0"/>
																<xsd:element name="PARTY_ATTAIN_AGE"  type="xsd:nonNegativeInteger"  minOccurs="0"/>
																<xsd:element name="PARTY_GENDER" type="xsd:string"  minOccurs="0" />
																<xsd:element name="PARTY_GENDER_IDENTITY" type="xsd:string" minOccurs="0"/>

																<xsd:element name="EMAIL_ADDRESSES" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element maxOccurs="unbounded" name="EMAIL_ADDRESS" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="EMAIL_ADDR" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="EMAIL_TYPE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="STATUS" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="EMAIL_PREFERRED_IND" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="EMAIL_CLASSIFICATION" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="PERSONAL" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="BUSINESS" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="OTHER" type="xsd:string" minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="PARTY_PHONES" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element maxOccurs="unbounded" name="PARTY_PHONE" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="PHN_NUM" type="xsd:string" minOccurs="0" />
																						<xsd:element name="PHN_EXT" type="PhoneNumber" minOccurs="0" />
																						<xsd:element name="PHN_TYPE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="STATUS" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="PHN_PREFERRED_IND" type="xsd:string"  minOccurs="0" />
																						<xsd:element name="PHN_CLASSIFICATION" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="MOBILE" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="BUSINESS" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="HOME" type="xsd:string" minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="PREFERRED" type="xsd:string"  minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="CARRIER" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="CARRIER_CONT" type="AlphanumericString"  minOccurs="0"/>
													<xsd:element name="CARRIER_ID" type="AlphanumericString"  minOccurs="0"/>
													<xsd:element name="CARRIER_CODE" type="AlphanumericString"  minOccurs="0"/>
													<xsd:element name="CARRIER_NAIC_CODE" type="AlphanumericString"  minOccurs="0"/>
													<xsd:element name="CARRIER_ORG_CODE" type="AlphanumericString"  minOccurs="0"/>
													<xsd:element name="CARRIER_DISPLAY_NAME" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="CARRIER_BUSINESS_NAME" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="CARRIER_ADDRESSES">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="CARRIER_ADDRESS" maxOccurs="unbounded">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="CARRIER_ADDR_LINE1" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="CARRIER_ADDR_LINE2" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="CARRIER_ADDR_LINE3" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="CARRIER_CITY" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="CARRIER_STATE" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="CARRIER_ZIP" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="CARRIER_ZIP_EXTN" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="CARRIER_ADDR_COUNTRY_CODE" type="xsd:string"  minOccurs="0" />
																			<xsd:element name="CARRIER_ADDR_TYPE" type="xsd:string"  minOccurs="0" />
																			<xsd:element name="CARRIER_ADDR_STATUS" type="xsd:string"  minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
													<xsd:element name="CARRIER_PHONES">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element maxOccurs="unbounded" name="CARRIER_PHONE">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="CARRIER_PHN_NUM" type="PhoneNumber" minOccurs="0"/>
																			<xsd:element name="CARRIER_PHN_TYPE" type="xsd:string"  minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
													<xsd:element name="CARRIER_EMAILS" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element maxOccurs="unbounded" name="CARRIER_EMAIL" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="CARRIER_EMAIL_ADDR" type="Email"  minOccurs="0"/>
																			<xsd:element name="CARRIER_EMAIL_TYPE" type="xsd:string"  minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
													<xsd:element name="CARRIER_WEB_LINKS"  minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element maxOccurs="unbounded" name="CARRIER_WEB_LINK"  minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="CARRIER_WEB_LINK_URL" type="xsd:anyURI"  minOccurs="0"/>
																			<xsd:element name="CUSTOMER_WEB_LINK_URL" type="xsd:anyURI" minOccurs="0"/>
																			<xsd:element name="AGENT_WEB_LINK_URL" type="xsd:anyURI" minOccurs="0"/>
																			<xsd:element name="CARRIER_WEB_LINK_TYPE" type="xsd:string"  minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
													<xsd:element name="CARRIER_OFFICE_HOURS" type="AlphanumericString"  minOccurs="0"/>
													<xsd:element name="CARRIER_OFFICE_DAYS" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="CARRIER_BUDGET_CNTR" type="xsd:string"  minOccurs="0"/>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="CARRIER_INFORMATION" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="CARRIER" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="CARRIER_CONT" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="CARRIER_ID" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="CARRIER_CODE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="CARRIER_NAIC_CODE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="CARRIER_ORG_CODE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="CARRIER_DISPLAY_NAME" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="CARRIER_BUSINESS_NAME" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="CARRIER_ADDRESSES"  minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="CARRIER_ADDRESS" maxOccurs="unbounded"  minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="CARRIER_ADDR_LINE1" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="CARRIER_ADDR_LINE2" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="CARRIER_ADDR_LINE3" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="CARRIER_CITY" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="CARRIER_STATE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="CARRIER_ZIP" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="CARRIER_ZIP_EXTN" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="CARRIER_ADDR_COUNTRY_CODE" type="xsd:string"  minOccurs="0" />
																						<xsd:element name="CARRIER_ADDR_TYPE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="CARRIER_ADDR_STATUS" type="xsd:string"  minOccurs="0" />
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="CARRIER_PHONES"  minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element maxOccurs="unbounded" name="CARRIER_PHONE"  minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="CARRIER_PHN_NUM" type="PhoneNumber" minOccurs="0"/>
																						<xsd:element name="CARRIER_PHN_TYPE" type="xsd:string"  minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="CARRIER_EMAILS" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element maxOccurs="unbounded" name="CARRIER_EMAIL" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="CARRIER_EMAIL_ADDR" type="Email"  minOccurs="0"/>
																						<xsd:element name="CARRIER_EMAIL_TYPE" type="xsd:string"  minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="CARRIER_WEB_LINKS"  minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element maxOccurs="unbounded" name="CARRIER_WEB_LINK"  minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="CARRIER_WEB_LINK_URL" type="xsd:anyURI"  minOccurs="0"/>
																						<xsd:element name="CARRIER_WEB_LINK_TYPE" type="xsd:string"  minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="CARRIER_OFFICE_HOURS" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="CARRIER_OFFICE_DAYS" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="CARRIER_BUDGET_CNTR" type="xsd:string"  minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="EXCHANGES" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="EXCHANGE" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="EXC_ROLE_CODE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_TRNS_COMP_ACCT_NUM" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_COMP_ACCT_NUM" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_COMP_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_COMP_ADDR_LINE1" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_COMP_ADDR_LINE2" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_COMP_ADDR_LINE3" type="xsd:string" minOccurs="0" />
																<xsd:element name="EXC_COMP_CITY" type="xsd:string" minOccurs="0" />
																<xsd:element name="EXC_COMP_STATE" type="xsd:string" minOccurs="0" />
																<xsd:element name="EXC_COMP_ZIP" type="xsd:string" minOccurs="0" />
																<xsd:element name="EXC_DELIVERY_MODE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_FAX_NUM" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_ER_NUM" type="xsd:string" minOccurs="0"/>
																<xsd:element name="EXC_TOT_AMT" type="xsd:decimal" minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="FINANCIAL_INFORMATION" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="FINANCIAL_ANNUAL_INCOME" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_ANNUAL_EXPENSES" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_DISPOSABLE_INCOME" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_CHANGE_ACKNOWLEDGEMENT" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_RESIDENCE_TYPE" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_SUFFICIENT_FUND" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_TAX_BRACKET" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_NET_WORTH" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_LIQUID_ASSET" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_CABENEFIT" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_PRODUCTS" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_TOTAL_ANNUITY_VALUE" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_POTENTIAL_INTERESTS" type="xsd:string"  minOccurs="0"/>
													<xsd:element name="FINANCIAL_SOURCE_OF_FUNDS" type="xsd:string"   minOccurs="0"/>
													<xsd:element name="FINANCIAL_REPLACEMENTS" type="xsd:string"   minOccurs="0"/>
													<xsd:element name="FINANCIAL_DISTRIBUTIONS" type="xsd:string"   minOccurs="0"/>
													<xsd:element name="FINANCIAL_FIRST_DISTRIBUTION" type="xsd:string"   minOccurs="0"/>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYVALUES" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="PVAL_CONT" type="AlphanumericString" minOccurs="0"/>
													<xsd:element name="PVAL_CON_YEAR" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_FREE_DAYS" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_GUAR_ANNUAL_REDUCED_PAID_UP_AMOUNT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_GUAR_PCT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_BEGIN_DATE" type="OptionalDate" minOccurs="0"/>
													<xsd:element name="PVAL_END_DATE" type="OptionalDate" minOccurs="0"/>
													<xsd:element name="PVAL_BEGIN_COVER_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_END_COVER_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CHG_COVER_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_BEGIN_DEATH_BNFT_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_END_DEATH_BNFT_VAL" type="xsd:string" minOccurs="0" />
													<xsd:element name="PVAL_CHG_DEATH_BNFT_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_BEGIN_SURR_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_END_SURR_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CHG_SURR_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_BEGIN_ACC_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_END_ACC_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CHG_ACC_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_PAY_RCVD" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_COI_COST" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_COST_OF_INS_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_UNIT_CHARGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_EXPENSE_CHARGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_PART_WITHDRAW_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_LOAN_AMT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_LOAN_BALN_AMT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_LOAN_REPAY_AMT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_INT_CHARGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_TOTAL_INT_CREDIT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_INT_CREDIT_CURR_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_LOAN_INT_CURR_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_LOAN_CREDIT_CURR_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_INT_CREDIT_NEXT_YR_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_LOAN_INT_NEXT_YR_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_LOAN_CREDIT_NEXT_YR_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CURR_PAY_AMT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CURR_PAY_MODE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_ANN_LPS_AMT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_INITIAL_PREMIUM_REQUEST_AMOUNT" type="xsd:string" />
													<xsd:element name="PVAL_INITIAL_PAYMENT_AMOUNT_RECEIVED_DATE" type="OptionalDate" minOccurs="0" />
													<xsd:element name="PVAL_GUARANTEED_INTEREST_RATE" type="xsd:string" minOccurs="0" />
													<xsd:element name="PVAL_FIXED_GUARANTEED_MINI_INTEREST_RATE" type="xsd:string"  minOccurs="0" />
													<xsd:element name="PVAL_GUARANTEED_MONTHLY_EXPENSE_CHARGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_GUARANTEED_MONTHLY_POST_EXPENSE_CHARGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_GUARANTEED_MONTHLY_UNIT_CHARGE_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_GUARANTEED_MONTHLY_POST_UNIT_CHARGE_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_PAYMENT_CHARGE_PERCENTAGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_PAYMENT_CHARGE_PERCENTAGE_1" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_PAYMENT_CHARGE_PERCENTAGE_2" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_PAYMENT_CHARGE_PERCENTAGE_3" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_MIN_REQUIRED_PREMIUM_AMOUNT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_INITIAL_PREMIUM_RECEIVED_AMOUNT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_INITIAL_INT_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="WITHDRAWAL_MAX_ANNUAL_PERCENTAGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="GAUR_SUBSEQ_PERCENTAGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="GAUR_INITIAL_PERCENTAGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_GUAR_MIN_INT_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_MIN_INIT_PCT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_MIN_SUB_PCT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_GUAR_MIN_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_GUAR_SURR_PERCENTAGE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_NF_FORFEITURE_RATE" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_WITHDRAWAL_FREE_PCT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_MVA_INDEX" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_WITHDRAWAL_MIN_AMT" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CONTRACT_MIN_VAL" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_SURR_CHRG_TERM_PERC" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CURR_PERCENTAGE_20" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CURR_PERCENTAGE_21" type="xsd:string" minOccurs="0"/>
													<xsd:element name="PVAL_CURR_PERCENTAGE_31" type="xsd:string" minOccurs="0"/>
                          <xsd:element name="PVAL_RIDER_MIN_ACCELE_AMOUNT" type="xsd:string" minOccurs="0"/>
                          <xsd:element name="PVAL_LOAN_MAX_INTEREST_RATE" type="xsd:string" minOccurs="0"/>
                          <xsd:element name="PVAL_LOAN_MIN_AMT" type="xsd:string" minOccurs="0"/>
                          <xsd:element name="PVAL_LOAN_MIN_REPAYMENT" type="xsd:string" minOccurs="0"/>
                          <xsd:element name="PVAL_GUARANTEE_MAX_EXPENSE_CHARGE_RATE" type="xsd:string" minOccurs="0"/>                          
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="SURRENDERCHARGES" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="SURRENDERCHARGE" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="SURR_CHRG_TYPE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="SURR_CHRG_TERM_PERC" type="xsd:string" minOccurs="0"/>
																<xsd:element name="SURR_CHRG_TERM_YEAR" type="xsd:int" minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYCOVERAGES" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element maxOccurs="unbounded" name="COVERAGES"  minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="TOTAL_COVERAGE_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="CUM_GROSS_DEATHBENEFIT_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="NET_DEATH_BENEFIT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="REMAINING_DEATH_BENEFIT_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="MIN_COVERAGE_AMOUNT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="COVERAGE_MAX_ANNUAL_COVERAGE_CHANGE_ALLOWED_PER_POLICY" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="COVERAGE_DEATH_BENEFIT_OPTION_START_DATE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="COVERAGE_DEATH_BENEFIT_TEST_OPTION" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="COVERAGE_AVAL_DEATH_BENEFIT_OPTION" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="COVERAGE_RISK_CLASS_CHANGE_ALLOWED_YEAR" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="COVERAGE_CHARGE_RATE_MONTHLY" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="COVERAGE_ASSEST_CHARGE_RATE_MONTHLY" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="MAX_COVERAGE_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="COVERAGE_CHANGE_EFF_DATE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="COVERAGE_BAND" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="MAX_ANN_COV_CHANGE_ALLOW_PER_POLICY" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="MIN_COVERAGE_DECREASE_AMOUNT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="MAX_COVERAGE_DECREASE_AMOUNT" type="xsd:unsignedByte"   minOccurs="0"/>
																<xsd:element name="MAX_AGE_NO_COVERAGE_AMT_DECREASE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="COVERAGE_AMT_DECREASE_ALLOWED" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="MAX_AGE_NO_COVERAGE_AMT_INCREASE" type="xsd:unsignedByte"   minOccurs="0"/>
																<xsd:element name="COVERAGE_AMT_INCREASE_ALLOWED" type="xsd:unsignedByte"   minOccurs="0"/>
																<xsd:element name="MIN_COVERAGE_INCREASE_AMOUNT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="MAX_COVERAGE_INCREASE_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="COVERAGE_RISK_CLASS_CHANGE_COUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="COVERAGE">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="COVERAGE_ID" type="AlphanumericString"  minOccurs="0"/>
																			<xsd:element name="COVERAGE_TYPE" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="COVERAGE_NAME" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="CURRENT_AMOUNT" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="ANNUAL_PREMIUM" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="ORIGINAL_COVERAGE_AMOUNT" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="MIN_COVERAGE_AMOUNT" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="MAX_COVERAGE_AMOUNT" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="GROSS_DEATHBENEFIT_AMOUNT" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="LOW_DEATHBENEFIT_AMOUNT" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="COVERAGE_CHANGE_AMOUNT" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="COVERAGE_ISSUE_AGE" type="xsd:string" minOccurs="0"/>

																			<xsd:element name="COVERAGE_EFF_DATE" type="OptionalDate" minOccurs="0"/>
																			<xsd:element name="COVERAGE_CHANGE_EFF_DATE" type="OptionalDate" minOccurs="0"/>
																			<xsd:element name="COVERAGE_TERMINATION_DATE" type="OptionalDate" minOccurs="0"/>
																			<xsd:element name="UNIT_OF_COVERAGE" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="VALUE_PER_UNIT_OF_COVERAGE" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="GUIDELINE_SINGLE_PREMIUM" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="GUIDELINE_LEVEL_PREMIUM" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="SEVEN_PAY_PREMIUM" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="COVERAGE_PARTICIPANTS" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="PARTICIPANT" maxOccurs="unbounded" minOccurs="0">
																							<xsd:complexType>
																								<xsd:sequence>
																									<xsd:element name="PARTYID" type="AlphanumericString"  minOccurs="0"/>
																									<xsd:element name="SUBSTANDARD_RATING" type="xsd:string"   minOccurs="0"/>
																									<xsd:element name="FLATEXTRA"  minOccurs="0">
																										<xsd:complexType>
																											<xsd:sequence>
																												<xsd:element name="FLATEXTRA_TYPE" type="xsd:string"  minOccurs="0"/>
																												<xsd:element name="FLATEXTRA_DURATION" type="xsd:int"  minOccurs="0"/>
																												<xsd:element name="FLATEXTRA_AMOUNT" type="xsd:string"  minOccurs="0"/>
																												<xsd:element name="FLATEXTRA_RATE" type="xsd:string"  minOccurs="0"/>
																												<xsd:element name="FLATEXTRA_START_DATE" type="OptionalDate" minOccurs="0"/>
																											</xsd:sequence>
																										</xsd:complexType>
																									</xsd:element>
																								</xsd:sequence>
																							</xsd:complexType>
																						</xsd:element>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYLOANS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYLOAN" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType mixed="true">
															<xsd:sequence>
																<xsd:element name="PLOAN_CONT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PLOAN_ID" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PLOAN_PREM_MODE_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="PLOAN_DUE_DATE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="PLOAN_PREM_DUE_DATE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="PLOAN_SURR_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="PLOAN_CASH_SURR_VALUE" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="PLOAN_BALANCE" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="LOAN_TOTAL_BALANCE" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="LOAN_TOTAL_PRINCIPAL" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="LOAN_PAYOFF_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="LOAN_MAX_AMT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_MIN_AMT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_TOTAL_ACCRUED_INT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_MAX_INTEREST_RATE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="LOAN_CURRENT_INTEREST_RATE" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="LOAN_LAST_INT_DUEDATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="LOAN_TOTAL_NO_OF_LOAN" type="xsd:int"   minOccurs="0"/>
																<xsd:element name="LOAN_INTEREST_METHOD" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="LOAN_TOTAL_YTD_TAKEN" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="LOAN_REPAYMENT_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="LOAN_MIN_REPAYMENT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="LOAN_CHARGE_INTEREST_RATE" type="xsd:string"   minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="WITHDRAWALVALUES" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="WITHDRAWALVALUE" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="WITHDRAWAL_TOTAL_AMT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_FREE_AMT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_MIN_AMT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_MAX_AMT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_MAX_PERCENTAGE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_ANNUAL_LIMIT_NO_COV_DECREASE" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_ANNUAL_PERCENTAGE_NO_COV_DECREASE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_MAX_REQUEST_DURING_VESTING_PERIOD" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_MAX_REQUEST_AFTER_VESTING_PERIOD" type="xsd:int"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_TAKEN_TOTAL_YTD" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_NUMBER_OF_WITHDRAWAL" type="xsd:int"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_ALLOWED_START_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_ALLOWED_START_YEAR" type="xsd:int"  minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_MAX_AGE_NO_COVERAGE_AMT_DECREASE" type="xsd:string"  minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="LOANSEGMENTS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="LOAN" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="LOAN_ID" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="LOAN_TYPE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_INTEREST_TYPE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_INTEREST_DUE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_PRINCIPAL" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_BALANCE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_COLLATERAL_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_INTEREST_RATE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_CREDIT_RATE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_ACCRUED_INTEREST" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_COLLATERAL_ACCRUED_INTEREST" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_TOTAL_YTD_TAKEN" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_GL_FUNDCODE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_START_DATE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="LOAN_END_DATE" type="OptionalDate" minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYMATCH" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="MATCH" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="MATCH_ID" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="MATCH_ACC_VALUE" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="MATCH_CUMM_PAY_AMT" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="MATCH_YTD_VALUE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="MATCH_MAX_LIFE_VEST_AMT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="MATCH_PERCENTAGE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="MATCH_ANN_VEST_AMT" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="MATCH_MIN_PAYMENT" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="MATCH_PERIOD" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="MATCH_START_DATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="MATCH_END_DATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="MATCH_VESTING_PERIOD" type="xsd:int"   minOccurs="0"/>
																<xsd:element name="MATCH_VESTING_START_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="MATCH_VESTING_END_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="MATCH_GL_FUND_CODE" type="xsd:string"   minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYPREMIUMS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYPREMIUM" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="PREMIUM_MODE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="PREMIUM_AMT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="PREMIUM_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="PREMIUM_START_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="PREMIUM_LAST_PAY_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="PREMIUM_NEXT_PAY_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="PREMIUM_MIN_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="PREMIUM_DUE_DATE" type="OptionalDate" minOccurs="0" />
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYPAYMENTS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYPAYMENT" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="PAYMENT_ARR_ID" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYMENT_ARR_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="REASON" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="PAYMENT_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="PAYMENT_STATUS" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="PAYMENT_FORM" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="PAYMENT_FREQUENCY" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="PAYMENT_NUMBER_OF_OCCURRENCE" type="xsd:int"  minOccurs="0"/>
																<xsd:element name="PAYMENT_DISBURSEMENT_TYPE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYMENT_AMT" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="PAYMENT_REQUEST_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="PAYMENT_BEGIN_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="PAYMENT_END_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="PAYMENT_LAST_ACTIVITY_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="PAYMENT_NEXT_ACTIVITY_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="PAYMENT_PARTY_DETAILS" minOccurs="0" >
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="PAYMENTPARTYROLE" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="PAYMENT_PARTY_ROLE" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="PAYMENT_PARTY_ID" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="PAYMENT_BANK_ID" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="PAYMENT_PERCENTAGE" type="xsd:string"  minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="FUNDS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="FUND" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="FND_SEG_ID" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FND_CONT" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FND_ACC_CODE" type="AlphanumericString" minOccurs="0"/>
																<xsd:element name="FND_DIV_CODE" type="AlphanumericString" minOccurs="0"/>
																<xsd:element name="FND_FUND_MKTG_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FND_FUND_CORR_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FND_ACC_TYPE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FND_ACC_SUB_TYPE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FND_END_BAL" type="xsd:string" minOccurs="0" />
																<xsd:element name="FND_ALLOC_PERC" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FND_RENEW_DATE" type="AlphanumericString" minOccurs="0"/>
																<xsd:element name="FND_FUND_SORT_ORDER" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="FND_CLOSING_IND" type="AlphanumericString" minOccurs="0"/>
																<xsd:element name="FND_MAX_ALLOC_PERC" type="xsd:decimal" minOccurs="0"/>
																<xsd:element name="FND_INT_RATE" type="xsd:decimal" minOccurs="0"/>
																<xsd:element name="FND_GUAR_PRD" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FND_DEPOSIT_DATE" type="AlphanumericString" minOccurs="0"/>
																<xsd:element name="FND_IND_CAP_RATE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FND_GUAR_MIN_CAP_RATE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FND_IND_PART_RATE" type="xsd:string"  minOccurs="0" />
																<xsd:element name="FND_GUAR_MIN_PART_RATE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FND_IND_TRIGGER" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FND_GUAR_MIN_TRIGGER" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FND_INDEX_NAME" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FND_GUAR_MIN_INT_RATE" type="xsd:string"  minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYWITHDRAWALS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYWITHDRAWAL" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="WITHDRAWAL_ARR_TYPE"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_ARR_ID" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_STATUS" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_FORM" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_FREQUENCY" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_NUMBER_OF_OCCURRENCE" type="xsd:int"  minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_DISBURSEMENT_TYPE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_AMT" type="xsd:unsignedByte"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_REQUEST_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_BEGIN_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_END_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="WITHDRAWALT_LAST_ACTIVITY_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_NEXT_ACTIVITY_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="WITHDRAWAL_PARTY_DETAILS"  minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="WITHDRAWAL_PARTY" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="WITHDRAWAL_PARTY_ROLE" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="WITHDRAWAL_PARTY_ID" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="WITHDRAWAL_BANK_ID" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="WITHDRAWAL_PERCENTAGE" type="xsd:unsignedByte"   minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYBANKINFO" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYBANK" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="BANK_ID" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_PARTY_ID" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_START_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="BANK_END_DATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="BANK_STATUS" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_ACC_TOKEN" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="BANK_HOLD_NAME" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_NAME" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_ACC_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_ROUT_NUM" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_ACC_NUM" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_CREDIT_CARD_NUM" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="BANK_CREDIT_EXP_DATE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="BANK_CREDIT_CARD_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_CREDIT_DEBIT_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_INSTITUTION_NUM" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_ACC_PURPOSE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="BANK_LAST_UPDATE_DATETIME" type="xsd:string"  minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYFUNDS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYFUND" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="FUND_ID" type="xsd:string" minOccurs="0" />
																<xsd:element name="FUND_PRODUCT_CODE" type="AlphanumericString" minOccurs="0" />
																<xsd:element name="FUND_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FUND_ACCOUNT_TYPE" type="xsd:string" minOccurs="0" />
																<xsd:element name="FUND_INDEX_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FUND_ALLOCATION_PERCENTAGE" type="xsd:unsignedByte" minOccurs="0"/>
																<xsd:element name="FUND_GL_FUND_CODE" type="xsd:unsignedByte" minOccurs="0"/>
																<xsd:element name="FUND_TOTAL_FUND_VALUE" type="xsd:decimal" minOccurs="0"/>
																<xsd:element name="FUND_START_DATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="FUND_END_DATE" type="OptionalDate" minOccurs="0"/>
                                								<xsd:element name="FUND_ACCOUNT_NAME" type="xsd:string" minOccurs="0"/>
                              									<xsd:element name="FUND_GUARANTEED_MIN_CAP_RATE" type="xsd:decimal" minOccurs="0"/>
                                								<xsd:element name="FUND_GUARANTEED_MIN_PARTICIPATION_RATE" type="xsd:decimal" minOccurs="0"/>
																<xsd:element name="FUND_SEGMENTS" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="FUND_SEGMENT" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="FUND_SEGMENT_ID" type="xsd:unsignedByte" minOccurs="0"/>
																						<xsd:element name="FUND_ID" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_ORIGINAL_DEPOSIT_AMOUNT" type="xsd:decimal" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_ORIGINAL_DEPOSIT_DATE" type="OptionalDate" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_DEPOSIT_DATE" type="OptionalDate" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_DEPOSIT_AMOUNT" type="xsd:decimal" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_CURRENT_AMOUNT" type="xsd:decimal" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_RENEWAL_DATE" type="OptionalDate" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_NUMBER_OF_UNITS" type="xsd:string" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_SWEEP_ACCOUNT_ID" type="AlphanumericString" minOccurs="0" />
																						<xsd:element name="FUND_SEGMENT_START_DATE" type="xsd:unsignedByte" minOccurs="0"/>
																						<xsd:element name="FUND_SEGMENT_END_DATE" type="OptionalDate" minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="FUND_MIN_HOLDING_ACC_TRANSFER_AMT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FUND_MIN_FUND_TRANSFER_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="FUND_MIN_REQUIRED_ACCOUNT_VALUE" type="xsd:decimal"  minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYFUNDSINFO" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYFUNDINFO" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="FUND_ID" type="xsd:string" minOccurs="0" />
																<xsd:element name="FUND_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FUND_INITIAL_PAYMENT" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FUND_INFO_SEGMENTS" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="FUND_INFO_SEGMENT" maxOccurs="unbounded" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="FUND_ID" type="xsd:string" minOccurs="0" />
																						<xsd:element name="FUND_SEGMENT_INITIAL_INDEX" type="xsd:string" minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="FUND_MIN_HOLDING_ACC_TRANSFER_AMT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FUND_MIN_FUND_TRANSFER_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="FUND_MIN_REQUIRED_ACCOUNT_VALUE" type="xsd:decimal"  minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="FEATURES" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="FEATURE" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="FEATURE_CODE" type="AlphanumericString"   minOccurs="0"/>
																<xsd:element name="FEATURE_EXT_CODE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="FEATURE_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="FEATURE_NAME" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="FEATURE_GROUP_ID" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="FEATURE_STATUS" type="xsd:boolean"  minOccurs="0"/>
																<xsd:element name="FEATURE_START_DATE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="FEATURE_END_DATE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="FEATURE_EFF_DATE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="FEATURE_APPROVE_DATE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="FEATURE_TOT_REQ_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="FEATURE_TOT_MIN_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="FEATURE_PAY_AMT" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="FEATURE_TOT_PAY_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="FEATURE_INDICATOR" type="xsd:string" minOccurs="0"/>
                                <xsd:element name="FEATURE_PAYMENT_FORM" type="xsd:string" minOccurs="0"/>
                                <xsd:element name="FEATURE_PERIOD_LAPSE_PROTECTION" type="xsd:int" minOccurs="0"/>
                                <xsd:element name="FEATURE_PAYMENT_AMOUNT_LAPSE_PROTECTION" type="xsd:decimal" minOccurs="0"/>
                                <xsd:element name="FEATURE_FREQUENCY" type="xsd:string" minOccurs="0"/>
																<xsd:element name="FEATURE_FEE" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="FEA_FEE_AMT" type="xsd:decimal"  minOccurs="0"/>
																			<xsd:element name="FEA_FEE_PCT" type="xsd:decimal"  minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
                    <xsd:element name="CHARGES" minOccurs="0">
                        <xsd:complexType>
                            <xsd:sequence>
                                <xsd:element name="CHARGE" maxOccurs="unbounded" minOccurs="0">
                                    <xsd:complexType>
                                        <xsd:sequence>
                                            <xsd:element name="CHARGE_COVERAGE_ID" type="xsd:string"   minOccurs="0"/>
                                            <xsd:element name="CHARGE_TYPE" type="xsd:string"   minOccurs="0"/>
                                            <xsd:element name="CURRENT_MONTH_CHARGE" type="xsd:decimal"   minOccurs="0"/>
                                            <xsd:element name="POLICY_CHARGES" type="xsd:string" minOccurs="0"/>
                                        </xsd:sequence>
                                    </xsd:complexType>
                                </xsd:element>
                            </xsd:sequence>
                        </xsd:complexType>
                    </xsd:element>
										<xsd:element name="POLICYRIDERS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYRIDER" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType mixed="true">
															<xsd:sequence>
																<xsd:element name="RIDER_TYPE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="RIDER_CODE" type="xsd:string" minOccurs="0" />
																<xsd:element name="RIDER_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="RIDER_ELECTED" type="xsd:string" minOccurs="0"/>
																<xsd:element name="RIDER_EFFECTIVE_DATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="RIDER_STATUS" type="xsd:string" minOccurs="0"/>
																<xsd:element name="RIDER_EXERCISE_DATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="RIDER_TERMINATION_DATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="RIDER_COVERAGE_ID" type="xsd:string" minOccurs="0"/>
																<xsd:element name="RIDER_AMOUNT" type="xsd:string" minOccurs="0"/>
																<xsd:element name="RIDER_COVERAGE_AMT" type="xsd:string" minOccurs="0"/>
																<xsd:element name="RIDER_CHARGE" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="RIDER_EXERCISE_CHARGE" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="RIDER_EXERCISE_CHARGE_RATE" type="xsd:string"  minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="RIDER_MIN_SAFEGAURD_AGE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="RIDER_MIN_SAFEGAURD_LOAN_PAYOFFAMT_PER" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="RIDER_MIN_SAFEGAURD_POLICY_YEAR" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="RIDER_TERMINAL_HEALTH_EVENT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="RIDER_MAX_ACCELE_BENEFIT_PERCENTAGE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="RIDER_MAX_BENEFIT_AMT" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="RIDER_MAX_CLAIM_COUNT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="RIDER_MAX_ADMIN_CHARGE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="RIDER_MIN_ACCELE_AMOUNT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="RIDER_MAX_ACCELE_AMOUNT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="RIDER_TIER_I_MAX_CRITICAL_ILLNESS_BENEFIT_PERCENTAGE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="RIDER_TIER_I_MAX_CRITICAL_ILLNESS_BENEFIT_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="RIDER_TIER_II_MAX_CRITICAL_ILLNESS_BENEFIT_PERCENTAGE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="RIDER_TIER_II_MAX_CRITICAL_ILLNESS_BENEFIT_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="RIDER_SIB_BONUS_INT_PER" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="RIDER_SIB_MAX_BONUS_AMOUNT" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="RIDER_MIN_BENEFIT_PCT" type="xsd:string"  minOccurs="0"/> <!-- New Field -->
																<xsd:element name="RIDER_LIFE_MAX_PERCENTAGE" type="xsd:string"  minOccurs="0"/> <!-- New Field -->
																<xsd:element name="RIDER_LIFE_MAX_AMOUNT" type="xsd:string"  minOccurs="0"/> <!-- New Field -->
																<xsd:element name="RIDER_PARTICIPANTS">
																<xsd:complexType>
																<xsd:sequence>
																	<xsd:element name="RIDER_PARTICIPANT" maxOccurs="unbounded" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																		<xsd:element name="PARTICIPANT_RISS_CLASS" type="xsd:string" minOccurs="0"/>
																		<xsd:element name="PARTICIPANT_SUBSTANDARD_RATING" type="xsd:string" minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																	</xsd:element>
																</xsd:sequence>
																</xsd:complexType>
															</xsd:element>

															<!-- Coverage Layers -->
															<xsd:element name="COVERAGE_LAYERS"  minOccurs="0">
																<xsd:complexType>
																<xsd:sequence>
																	<xsd:element name="COVERAGE_LAYER" maxOccurs="unbounded" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																		<xsd:element name="COVERAGE_ID" type="AlphanumericString" minOccurs="0"/>
																		<xsd:element name="CURRENT_AMOUNT" type="xsd:string" minOccurs="0"/>
																		<xsd:element name="ANNUAL_PREMIUM" type="xsd:string" minOccurs="0"/>
																		<xsd:element name="COVERAGE_EFF_DATE" type="OptionalDate" minOccurs="0"/>
																		<xsd:element name="COVERAGE_TERMINATION_DATE" type="OptionalDate" minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																	</xsd:element>
																</xsd:sequence>
																</xsd:complexType>
															</xsd:element>

															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="DEATH_BENEFIT" minOccurs="0" >
											<xsd:complexType>
												<xsd:sequence>              
													<xsd:element name="DEATH_BENEFIT_OPTION" type="xsd:string"   minOccurs="0"/>
													<xsd:element name="DEATH_BENEFIT_OPTION_EFFECTIVE_DATE" type="xsd:string"  minOccurs="0"/>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="TEST_VALUES" minOccurs="0" >
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="GUIDELINE_PREMIUM" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="GUIDELINE_PREMIUM_TEST_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="DEFINITION_OF_LIFE_INSURANCE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="GUIDELINE_SINGLE_PREMIUM" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="GUIDELINE_LEVEL_PREMIUM" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="AMOUNT_EXCESS_TO_GUIDELINE" type="xsd:unsignedByte"   minOccurs="0"/>
																<xsd:element name="TOTAL_GUIDELINE_LEVEL_PREMIUM_SINCE_ISSUE" type="xsd:decimal"   minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
													<xsd:element name="MODIFIED_ENDOWMENT_CONTRACT" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="MEC_TEST_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="AMOUNT_EXCESS_TO_MEC" type="xsd:unsignedByte"   minOccurs="0"/>
																<xsd:element name="MEC_STATUS_DATE" type="OptionalDate" minOccurs="0"/>
																<xsd:element name="MEC_STATUS" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="SEVEN_PAY_TEST_BASIS" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="SEVEN_PAY_PREMIUM" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="SEVEN_PAY_START_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="SEVEN_PAY_PERIOD" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="SEVEN_PAY_LIMIT" type="xsd:decimal"   minOccurs="0"/>
																<xsd:element name="YEAR_IN_PERIOD" type="xsd:unsignedByte"   minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="ILLUSTRATIONVALUES" minOccurs="0" >
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="ILL_MAX_CHARGE" type="xsd:unsignedByte"   minOccurs="0"/>
													<xsd:element name="ILL_COVERAGE_START_YEAR" type="xsd:string"   minOccurs="0"/>
													<xsd:element name="ILL_GURANTEED_ANNUAL_FACE_AMOUNT_FROM_LAST_YEAR" type="xsd:string" minOccurs="0"/>
													<xsd:element name="ILL_ANNUAL_ACCOUNT_VALUE_FROM_LAST_YEAR" type="xsd:string" minOccurs="0"/>
													<xsd:element name="ILL_GUARNTEED_ANNUAL_COVERAGES_BASE_MODALPREMIUM_LAST_YEAR" type="xsd:string" minOccurs="0"/>
													<xsd:element name="ILL_LAPSE_YEAR" type="xsd:string" minOccurs="0"/>
													<xsd:element name="ILL_GURANTEED_ANNUAL_COVERAGES_BASE_MODALPREMIUM" type="xsd:string" minOccurs="0"/>
													<xsd:element maxOccurs="unbounded" name="ILLUSTRATION" minOccurs="0" >
														<xsd:complexType mixed="true">
															<xsd:sequence>
																<xsd:element name="ILL_YEAR" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_POLICY_YEAR" type="xsd:nonNegativeInteger"   minOccurs="0"/>
																<xsd:element name="ILL_INSURED_AGE" type="xsd:nonNegativeInteger"   minOccurs="0"/>
																<xsd:element name="ILL_OWNER_AGE" type="xsd:nonNegativeInteger"   minOccurs="0"/>
																<xsd:element name="ILL_PREMIUM" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_ANNUAL_PREMIUM" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_MIN_GUAR_CONT_VAL" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_CURR_CONT_VAL" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_SURR_CHARGE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUAR_CONT_WITHDRAWAL_VAL" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_CURR_CONT_WITHDRAWAL_VAL" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUAR_IRR" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_CURR_IRR" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUARANTEE_MAX_EXPENSE_CHARGE_RATE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUARANTEE_UNIT_RATE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUARANTEE_MIN_UNIT_CHARGE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUARANTEE_MAX_UNIT_CHARGE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_NH_RATE_PER" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_NH_ANN_GUARANTEED" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_NH_ANN_ILLUSTRATED" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_FIX_MIN_CONT_WITHDRAWAL_VAL" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GV_MIN_CONT_VAL" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GV_MIN_CONT_WITHDRAWAL_VAL" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_DEFAULT_VAL" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_DEATH_BENEFIT" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_CASHSURRENDER_VALUE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_LIFE_SURRENDER_COST_INDEX" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_LIFE_NET_PAYMENT_COST_INDEX" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_SAFEGAURD_RIDER_CHARGES" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_SIB_MIN_SAVING_PAYMENTS" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUAR_MAX_COI_RATE_PER" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUAR_MAX_COI_RATE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_CORR_RATE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="ILL_GUAR_ANNUAL_PREMIUM" type="xsd:string" minOccurs="0"/>
																<xsd:element name="ILL_INFORCE_IND" type="xsd:string" minOccurs="0"/>
																<xsd:element name="ILL_GUAR_ANNUAL_ACC_VALUE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="ILL_GUAR_CASH_VALUE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="ILL_PREMIUM_AMOUNT" type="xsd:string" minOccurs="0"/>
																<xsd:element name="ILL_ANNUAL_SURR_CHARGE" type="xsd:string" minOccurs="0"/>
                                <xsd:element name="ILL_GUAR_ANNUAL_REDUCED_PAID_UP_AMOUNT" type="xsd:string" minOccurs="0"/>

																<xsd:element name="ILL_ANNUAL_DEATH_BNFT_AMOUNT" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GURANTEED_ANNUAL_COVERAGES_WAIVER_OF_PREMIUM_MODAL_PREM" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GURANTEED_ANNUAL_COVERAGES_ACCIDENTAL_DEATH_BENEFIT_MODAL_PREM" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GURANTEED_ANNUAL_COVERAGES_CHILDRENSTERM_MODAL_PREM" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GURANTEED_ANNUAL_FACE_AMOUNT" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GURANTEED_ANNUAL_ACCIDENTAL_DEATH_BNFT_FACE_AMOUNT" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GURANTEED_ANNUAL_COVERAGES_CHARITABLE_GIVING_FACE_AMOUNT" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GURANTEED_ANNUAL_COVERAGES_CHILDRENS_TERM_FACE_AMOUNT" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GUAR_MAX_MONTHLY_PER_CHARGE_RATE_AGE" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GUAR_FACTOR_AGE" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GUARANTEED_MAX_ANNUAL_TERM_PREMIUMS" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GUAR_AGE" type="xsd:string" minOccurs="0"/>

								<xsd:element name="ILL_ANNUAL_ACCOUNT_VALUE" type="xsd:string" minOccurs="0"/>

                                <xsd:element name="ILL_GUAR_FACTOR_YEAR" type="xsd:string" minOccurs="0"/>
								<xsd:element name="ILL_GUAR_PER" type="xsd:string" minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="POLICYTRANSACTIONS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="POLICYTRANSACTION" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="TRAN_ID" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="TRAN_TYPE" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="TRAN_ARR_ID" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="TRAN_REQUEST_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="TRAN_EFFECTIVE_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="TRAN_PROCESS_DATE" type="OptionalDate"   minOccurs="0"/>
																<xsd:element name="TRAN_STATUS" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="TRAN_ACTION" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="TRAN_REASON" type="xsd:string"   minOccurs="0"/>
																<xsd:element name="TRAN_AMOUNTS" minOccurs="0">
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="PAYMENT_AMOUNT" type="xsd:decimal"   minOccurs="0"/>
																			<xsd:element name="REQUESTED_AMOUNT" type="xsd:decimal"  minOccurs="0"/>
																			<xsd:element name="APPLIED_AMOUNT" type="xsd:decimal"  minOccurs="0"/>
																			<xsd:element name="CALCULATED_AMOUNT" type="xsd:decimal"  minOccurs="0"/>
																			<xsd:element name="CHARGE_AMOUNT" type="xsd:decimal"  minOccurs="0"/>
																			<xsd:element name="TAXABLE_AMOUNT" type="xsd:decimal"  minOccurs="0"/>
																			<xsd:element name="AMOUNT_TYPE" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="REQUESTED_FUNDTRANSFER_AMOUNT" type="xsd:string" minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="PAYOR_ROLE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYOR_PARTY_ID" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYOR_PAYMENT_FORM" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYOR_BANK_ID" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYEE_ROLE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYEE_PARTY_ID" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYEE_PAYMENT_FORM" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="PAYEE_BANK_ID" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="FUND_DISTRIBUTIONS" minOccurs="0" >
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="DISTRIBUTION" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="FUND_ID" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="FUND_NAME" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="TOTAL_FUND_VALUE" type="xsd:unsignedByte"   minOccurs="0"/>
																						<xsd:element name="REQUESTED_AMOUNT" type="xsd:unsignedByte"   minOccurs="0"/>
																						<xsd:element name="FUND_DISTRIBUTION_SEGMENTS" minOccurs="0">
																							<xsd:complexType>
																								<xsd:sequence>
																									<xsd:element name="SEGMENT_ID" type="xsd:string"   minOccurs="0"/>
																									<xsd:element name="CURRENT_AMOUNT" type="xsd:unsignedByte"   minOccurs="0"/>
																									<xsd:element name="REQUESTED_AMOUNT" type="xsd:unsignedByte"   minOccurs="0"/>
																								</xsd:sequence>
																							</xsd:complexType>
																						</xsd:element>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="FUND_ACTIVITIES" minOccurs="0" >
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="ACTIVITY" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="FUND_ID" type="xsd:string"   minOccurs="0"/>
																						<xsd:element name="TOTAL_FUND_VALUE" type="xsd:unsignedByte"   minOccurs="0"/>
																						<xsd:element name="FUND_ACTIVITY_SEGMENTS" minOccurs="0">
																							<xsd:complexType>
																								<xsd:sequence>
																									<xsd:element name="SEGMENT_ID" type="xsd:string"   minOccurs="0"/>
																									<xsd:element name="CURRENT_AMOUNT" type="xsd:unsignedByte"   minOccurs="0"/>
																									<xsd:element name="SEGMENT_ACTIVITY" minOccurs="0">
																										<xsd:complexType>
																											<xsd:sequence>
																												<xsd:element name="SEGMENT_ID" type="xsd:string"   minOccurs="0"/>
																												<xsd:element name="APPLIED_RATE" type="xsd:unsignedByte"   minOccurs="0"/>
																												<xsd:element name="APPLIED_AMOUNT" type="xsd:unsignedByte"   minOccurs="0"/>
																											</xsd:sequence>
																										</xsd:complexType>
																									</xsd:element>
																								</xsd:sequence>
																							</xsd:complexType>
																						</xsd:element>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="LOAN_ACTIVITIES" minOccurs="0" >
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="LOAN_ACTIVTY" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="TOTAL_LOAN_BALANCE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TOTAL_LOAN_ACCRUED_INTEREST" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="LOAN_SEGMENTS" minOccurs="0">
																							<xsd:complexType>
																								<xsd:sequence>
																									<xsd:element name="SEGMENT_ID" type="AlphanumericString"  minOccurs="0"/>
																									<xsd:element name="LOAN_BALANCE" type="xsd:string"  minOccurs="0"/>
																									<xsd:element name="LOAN_ACCRUED_INTEREST" type="xsd:string"  minOccurs="0"/>
																									<xsd:element name="SEGMENT_ACTIVITY" minOccurs="0">
																										<xsd:complexType>
																											<xsd:sequence>
																												<xsd:element name="SEGMENT_ID" type="AlphanumericString"  minOccurs="0"/>
																												<xsd:element name="APPLIED_RATE" type="xsd:string"  minOccurs="0"/>
																												<xsd:element name="APPLIED_AMOUNT" type="xsd:string"  minOccurs="0"/>
																											</xsd:sequence>
																										</xsd:complexType>
																									</xsd:element>
																								</xsd:sequence>
																							</xsd:complexType>
																						</xsd:element>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="TRAN_PARTY" minOccurs="0" >
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="PARTY_ROLE" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="PARTY_ID" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="PERCENTAGE" type="xsd:unsignedByte"   minOccurs="0"/>
																			<xsd:element name="BANK_ID" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="PAYMENT_FORM" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="DISBURSEMENT_AMOUNT" type="xsd:string"  minOccurs="0"/>
																			<xsd:element name="GROSS_AMOUNT" type="xsd:string"  minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="TAX_WITHHOLDING_INSTRUCTIONS" minOccurs="0" >
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="PARTY_ROLE" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="PARTY_ID" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="TAX_WITHHOLDING_TYPE" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="TAX_RATE_TO_USE" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="FILING_STATUS" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="DOLLAR" type="xsd:unsignedByte"   minOccurs="0"/>
																			<xsd:element name="PERCENTAGE" type="xsd:unsignedByte"   minOccurs="0"/>
																			<xsd:element name="EXEMPTIONS" type="xsd:unsignedByte"   minOccurs="0"/>
																			<xsd:element name="TAX_JURISDICTION" type="xsd:string"   minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
																<xsd:element name="TAX_WITHHELD_AMOUNTS" minOccurs="0" >
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="PARTY_ROLE" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="PARTY_ID" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="TAX_WITHHOLDING_TYPE" type="xsd:string"   minOccurs="0"/>
																			<xsd:element name="WITHHELD_AMOUNT" type="xsd:unsignedByte"   minOccurs="0"/>
																			<xsd:element name="WITHHELD_TAXABLE_AMOUNT" type="xsd:unsignedByte"   minOccurs="0"/>
																			<xsd:element name="APPLIED_TAX_RATE" type="xsd:unsignedByte"   minOccurs="0"/>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="TRANSACTIONSUMMARIES" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="TRANSACTIONSUMMARY" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="TXN_CONT" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="TXN_TRANS_NUM" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="TXN_TRANS_DATE" type="AlphanumericString"  minOccurs="0"/>
																<xsd:element name="TXN_TRANS_TYPE" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="TXN_TRANS_DESC" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="TXN_TRANS_STATUS" type="xsd:string"  minOccurs="0"/>
																<xsd:element name="TXN_TOTAL_AMT" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="TXN_FED_TAX_WITHD" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="TXN_STATE_TAX_WITHD" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="TXN_BKUP_TAX_WITHD" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="TXN_NRA_TAX_WITHD" type="xsd:decimal"  minOccurs="0"/>
																<xsd:element name="TRANSACTIONDETAILS" minOccurs="0" >
																	<xsd:complexType>
																		<xsd:sequence>
																			<xsd:element name="TRANSACTIONDETAIL" maxOccurs="unbounded" minOccurs="0">
																				<xsd:complexType>
																					<xsd:sequence>
																						<xsd:element name="TXD_CONT" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_ACC_CODE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_DIV_CODE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_TRANS_NUM" type="AlphanumericString"  minOccurs="0"/>
																						<xsd:element name="TXD_TRANS_TYPE" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_TRANS_DESC" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_FUND_DIV_NAME" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_FUND_MKTG_NAME" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_TRANS_AMT" type="xsd:decimal"  minOccurs="0"/>
																						<xsd:element name="TXD_TRANS_DATE" type="AlphanumericString"  minOccurs="0"/>
																						<xsd:element name="TXD_UNIT_VALUE" type="xsd:decimal"  minOccurs="0"/>
																						<xsd:element name="TXD_TXN_UNITS" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_BEG_UNITS" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXD_END_UNITS" type="xsd:string"  minOccurs="0"/>
																						<xsd:element name="TXN_FUND_NAME" type="xsd:string"  minOccurs="0"/>
																					</xsd:sequence>
																				</xsd:complexType>
																			</xsd:element>
																		</xsd:sequence>
																	</xsd:complexType>
																</xsd:element>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="VENDORS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="VENDOR" maxOccurs="unbounded" minOccurs="0">
														<xsd:complexType>
															<xsd:sequence>
																<xsd:element name="VENDOR_BUSINESS_NAME" type="xsd:string" minOccurs="0"/>
																<xsd:element name="VENDOR_ADDR_LINE1" type="xsd:string" minOccurs="0"/>
																<xsd:element name="VENDOR_ADDR_LINE2" type="xsd:string" minOccurs="0"/>
																<xsd:element name="VENDOR_ADDR_LINE3" type="xsd:string" minOccurs="0"/>
																<xsd:element name="VENDOR_CITY" type="xsd:string" minOccurs="0"/>
																<xsd:element name="VENDOR_STATE" type="xsd:string" minOccurs="0"/>
																<xsd:element name="VENDOR_ZIP" type="xsd:string" minOccurs="0"/>
																<xsd:element name="VENDOR_PHN_NUM" type="PhoneNumber" minOccurs="0"/>
																<xsd:element name="VENDOR_EMAIL_ADDR" type="xsd:string" minOccurs="0"/>
																<xsd:element name="VENDOR_WEB_LINK_URL" type="xsd:anyURI" minOccurs="0"/>
																<xsd:element name="VENDOR_REASON_CODE" type="xsd:string" minOccurs="0"/>
															</xsd:sequence>
														</xsd:complexType>
													</xsd:element>
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
										<xsd:element name="NIGO_DETAILS" minOccurs="0">
											<xsd:complexType>
												<xsd:sequence>
													<xsd:element name="ENTITY" maxOccurs="unbounded" minOccurs="0">
													<xsd:complexType>
														<xsd:sequence>
															<xsd:element name="ROLETYPE" type="xsd:string" minOccurs="0"/>
															<xsd:element name="REASONS" minOccurs="0">
																<xsd:complexType>
																	<xsd:sequence>
																			<xsd:element name="REASON1" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON2" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON3" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON4" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON5" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON6" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON7" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON8" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON9" type="xsd:string" minOccurs="0"/>
																			<xsd:element name="REASON10" type="xsd:string" minOccurs="0"/>
																	</xsd:sequence>
																</xsd:complexType>
															</xsd:element>
														</xsd:sequence>
													</xsd:complexType>
													</xsd:element>	
												</xsd:sequence>
											</xsd:complexType>
										</xsd:element>
									</xsd:sequence>
								</xsd:complexType>
							</xsd:element>
						</xsd:sequence>
					</xsd:complexType>
				</xsd:element>
			</xsd:sequence>
		</xsd:complexType>
	</xsd:element>
</xsd:schema>